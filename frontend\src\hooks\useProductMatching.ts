import { useQuery } from "@tanstack/react-query";
import apiClient from "../lib/api";
import { Product, SearchFilters } from "../types";

interface ProductMatchingResult {
  mfr: string;
  total: number;
  products: Product[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    has_next: boolean;
    has_prev: boolean;
  };
}

interface UseProductMatchingProps {
  mfr?: string;
  enabled?: boolean;
  filters?: SearchFilters;
  limit?: number;
}

/**
 * Hook to find products with the same MFR code across different sellers
 * Optionally filters results based on active search filters
 */
export const useProductMatching = ({
  mfr,
  enabled = true,
  filters,
  limit = 20
}: UseProductMatchingProps) => {
  return useQuery({
    queryKey: ["product-matching", mfr, filters, limit],
    queryFn: async (): Promise<ProductMatchingResult> => {
      if (!mfr) {
        throw new Error("MFR code is required");
      }

      // Get all products with the same MFR code
      const response = await apiClient.client.get(`/products/match/${mfr}`, {
        params: { limit: 100 } // Get more results to filter locally if needed
      });

      let products = response.data.products || [];

      // Apply filters if provided
      if (filters && products.length > 0) {
        products = products.filter((product: Product) => {
          // Filter by category
          if (filters.category && product.category !== filters.category) {
            return false;
          }

          // Filter by manufacturer
          if (filters.manufactured_by && product.manufactured_by !== filters.manufactured_by) {
            return false;
          }

          // Filter by seller
          if (filters.seller && product.seller !== filters.seller) {
            return false;
          }

          return true;
        });
      }

      // Limit results
      const limitedProducts = products.slice(0, limit);

      return {
        mfr,
        total: products.length,
        products: limitedProducts,
        pagination: {
          page: 1,
          limit,
          total: products.length,
          has_next: products.length > limit,
          has_prev: false,
        },
      };
    },
    enabled: enabled && !!mfr,
    staleTime: 1000 * 60 * 10, // 10 minutes
    retry: 1,
  });
};

/**
 * Hook to get a single product by search term
 * Used to find the main product when a suggestion is clicked
 */
export const useProductByName = (productName: string, enabled = true) => {
  return useQuery({
    queryKey: ["product-by-name", productName],
    queryFn: async (): Promise<Product | null> => {
      if (!productName) return null;

      // Search for the specific product
      const response = await apiClient.search({
        q: productName,
        limit: 1,
        offset: 0,
      });

      const products = response.results || [];
      return products.length > 0 ? products[0] : null;
    },
    enabled: enabled && !!productName,
    staleTime: 1000 * 60 * 5, // 5 minutes
    retry: 1,
  });
};
