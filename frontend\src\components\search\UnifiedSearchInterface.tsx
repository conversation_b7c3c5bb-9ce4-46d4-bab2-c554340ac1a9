import { useState, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { Search, Filter, X } from "lucide-react";
import FilterDropdown from "./FilterDropdown";
import {
  useCategories,
  useManufacturers,
  useSellers,
  useSearchSuggestions,
  usePopularSearches,
} from "../../hooks/useSearch";
import { useQuickAddProduct } from "../../hooks/useShoppingLists";
import { useSearchStore } from "../../stores/searchStore";
import { useShoppingListStore } from "../../stores/shoppingListStore";
import { debounce } from "../../lib/utils";
import InPageProductDetails from "./InPageProductDetails";
import ErrorBoundary from "../ErrorBoundary";
import { ProductSuggestion } from "../../types";

interface UnifiedSearchInterfaceProps {
  showFilters?: boolean;
  placeholder?: string;
  className?: string;
}

const UnifiedSearchInterface = ({
  showFilters = true,
  placeholder = "Search dental products...",
  className = "",
}: UnifiedSearchInterfaceProps) => {
  const navigate = useNavigate();
  const inputRef = useRef<HTMLInputElement>(null);

  // Search state
  const [localQuery, setLocalQuery] = useState("");
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);

  // Product details state
  const [showProductDetails, setShowProductDetails] = useState(false);
  const [selectedProductName, setSelectedProductName] = useState("");

  // Filter state
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [selectedManufacturers, setSelectedManufacturers] = useState<string[]>(
    []
  );
  const [selectedSellers, setSelectedSellers] = useState<string[]>([]);

  // Store hooks
  const {
    setQuery,
    setFilters,
    addRecentSearch,
    recentSearches,
    popularSearches,
  } = useSearchStore();

  // Use React Query hook for quick-add functionality
  const quickAddMutation = useQuickAddProduct();

  // API hooks
  const {
    data: categoriesData,
    isLoading: categoriesLoading,
    error: categoriesError,
  } = useCategories();
  const {
    data: manufacturersData,
    isLoading: manufacturersLoading,
    error: manufacturersError,
  } = useManufacturers();
  const {
    data: sellersData,
    isLoading: sellersLoading,
    error: sellersError,
  } = useSellers();
  const { data: popularData } = usePopularSearches();

  // Safe data extraction with error handling
  const categories =
    categoriesData?.categories?.map((cat) => ({
      name: cat.category,
      count: cat.product_count,
    })) || [];
  const manufacturers =
    manufacturersData?.manufacturers?.map((mfr) => ({
      name: mfr.manufacturer || mfr.manufactured_by,
      count: mfr.product_count,
    })) || [];
  const sellers =
    sellersData?.sellers?.map((seller) => ({
      name: seller.seller,
      count: seller.product_count,
    })) || [];
  const popularSearchTerms =
    popularData?.popular_searches?.map((search) => search.term) || [];

  // Prepare filters for suggestions
  const suggestionFilters = {
    category: selectedCategories[0],
    manufactured_by: selectedManufacturers[0],
    seller: selectedSellers[0],
  };

  // Get filtered suggestions
  const { data: suggestionsData } = useSearchSuggestions(
    localQuery,
    localQuery.length >= 2 && showSuggestions,
    suggestionFilters
  );

  const suggestions = suggestionsData?.suggestions || [];

  // Debounced search function
  const debouncedSearch = debounce((searchQuery: string) => {
    if (searchQuery.trim()) {
      performSearch(searchQuery);
    }
  }, 300);

  const performSearch = (searchQuery: string) => {
    // Set filters in the store
    const filters: any = {};
    if (selectedCategories.length > 0) {
      filters.category = selectedCategories[0];
    }
    if (selectedManufacturers.length > 0) {
      filters.manufactured_by = selectedManufacturers[0];
    }
    if (selectedSellers.length > 0) {
      filters.seller = selectedSellers[0];
    }

    setFilters(filters);
    setQuery(searchQuery);
    addRecentSearch(searchQuery);

    // Navigate to search page with filters
    const params = new URLSearchParams();
    params.set("q", searchQuery);
    if (selectedCategories.length > 0) {
      params.set("category", selectedCategories[0]);
    }
    if (selectedManufacturers.length > 0) {
      params.set("manufactured_by", selectedManufacturers[0]);
    }
    if (selectedSellers.length > 0) {
      params.set("seller", selectedSellers[0]);
    }

    navigate(`/search?${params.toString()}`);
    setShowSuggestions(false);
  };

  // Handle input change
  const handleInputChange = (value: string) => {
    setLocalQuery(value);
    setSelectedIndex(-1);
    setShowSuggestions(true);
  };

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (localQuery.trim()) {
      performSearch(localQuery);
    }
  };

  // Handle suggestion selection - show product details instead of navigating
  const handleSuggestionSelect = (suggestion: string | ProductSuggestion) => {
    const productName =
      typeof suggestion === "string" ? suggestion : suggestion.name;
    setSelectedProductName(productName);
    setShowProductDetails(true);
    setShowSuggestions(false);
    setLocalQuery(productName);
    addRecentSearch(productName);
  };

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showSuggestions) return;

    const allSuggestions = [
      ...suggestions,
      ...recentSearches,
      ...popularSearchTerms,
    ];

    switch (e.key) {
      case "ArrowDown":
        e.preventDefault();
        setSelectedIndex((prev) =>
          prev < allSuggestions.length - 1 ? prev + 1 : prev
        );
        break;
      case "ArrowUp":
        e.preventDefault();
        setSelectedIndex((prev) => (prev > 0 ? prev - 1 : -1));
        break;
      case "Enter":
        e.preventDefault();
        if (selectedIndex >= 0 && allSuggestions[selectedIndex]) {
          handleSuggestionSelect(allSuggestions[selectedIndex]);
        } else {
          handleSubmit(e);
        }
        break;
      case "Escape":
        setShowSuggestions(false);
        setSelectedIndex(-1);
        break;
    }
  };

  // Clear all filters
  const handleClearFilters = () => {
    setSelectedCategories([]);
    setSelectedManufacturers([]);
    setSelectedSellers([]);
  };

  // Handle closing product details
  const handleCloseProductDetails = () => {
    setShowProductDetails(false);
    setSelectedProductName("");
  };

  // Handle adding product to shopping list
  const handleAddToList = async (
    suggestion: ProductSuggestion,
    event: React.MouseEvent
  ) => {
    event.stopPropagation(); // Prevent suggestion selection

    try {
      // Use the React Query mutation for quick-add functionality
      await quickAddMutation.mutateAsync({
        productId: suggestion.id,
        quantity: 1,
        product: suggestion,
      });

      // Show success notification
      window.dispatchEvent(
        new CustomEvent("shopping-list-success", {
          detail: {
            message: `Added "${suggestion.name}" to shopping list`,
            product: suggestion,
          },
        })
      );

      // Clear the search input after successful addition
      setLocalQuery("");
      setShowSuggestions(false);
    } catch (error) {
      console.error("Failed to add product to shopping list:", error);

      // Show error notification
      window.dispatchEvent(
        new CustomEvent("shopping-list-error", {
          detail: {
            message: `Failed to add "${suggestion.name}" to shopping list`,
            error,
          },
        })
      );
    }
  };

  // Check if any filters are selected
  const hasFilters =
    selectedCategories.length > 0 ||
    selectedManufacturers.length > 0 ||
    selectedSellers.length > 0;
  const totalSelected =
    selectedCategories.length +
    selectedManufacturers.length +
    selectedSellers.length;

  return (
    <div className={`w-full max-w-4xl mx-auto ${className}`}>
      <div className="bg-white border border-gray-200 rounded-xl shadow-sm p-6">
        {/* Filters Section */}
        {showFilters && (
          <div className="mb-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                <Filter className="h-5 w-5 text-primary-600" />
                <h3 className="text-lg font-semibold text-gray-900">
                  Advanced Filters
                </h3>
                {totalSelected > 0 && (
                  <span className="bg-primary-100 text-primary-700 text-xs font-medium px-2 py-1 rounded-full">
                    {totalSelected} selected
                  </span>
                )}
              </div>
              {hasFilters && (
                <button
                  onClick={handleClearFilters}
                  className="text-sm text-gray-500 hover:text-gray-700 font-medium"
                >
                  Clear all
                </button>
              )}
            </div>

            {/* Filter Dropdowns */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <FilterDropdown
                label="Categories"
                placeholder="Select categories"
                options={categories}
                selectedValues={selectedCategories}
                onSelectionChange={setSelectedCategories}
                loading={categoriesLoading}
                multiSelect={false}
              />

              <FilterDropdown
                label="Manufacturers"
                placeholder="Select manufacturers"
                options={manufacturers}
                selectedValues={selectedManufacturers}
                onSelectionChange={setSelectedManufacturers}
                loading={manufacturersLoading}
                multiSelect={false}
              />

              <FilterDropdown
                label="Sellers"
                placeholder="Select sellers"
                options={sellers}
                selectedValues={selectedSellers}
                onSelectionChange={setSelectedSellers}
                loading={sellersLoading}
                multiSelect={false}
              />
            </div>
          </div>
        )}

        {/* Search Input Section */}
        <div className="relative">
          <form onSubmit={handleSubmit} className="relative">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                ref={inputRef}
                type="text"
                value={localQuery}
                onChange={(e) => handleInputChange(e.target.value)}
                onKeyDown={handleKeyDown}
                onFocus={() => setShowSuggestions(true)}
                placeholder={placeholder}
                className="w-full pl-10 pr-10 py-4 text-lg border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent bg-white shadow-sm"
              />
              {localQuery && (
                <button
                  type="button"
                  onClick={() => {
                    setLocalQuery("");
                    setShowSuggestions(false);
                    inputRef.current?.focus();
                  }}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
                >
                  <X className="h-5 w-5" />
                </button>
              )}
            </div>
          </form>

          {/* Suggestions Dropdown */}
          {showSuggestions &&
            (suggestions.length > 0 ||
              recentSearches.length > 0 ||
              popularSearchTerms.length > 0) && (
              <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-xl z-50 max-h-80 overflow-y-auto">
                {/* Filtered Suggestions */}
                {suggestions.length > 0 && (
                  <div className="p-3 border-b border-gray-100">
                    <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">
                      {hasFilters ? "Filtered Suggestions" : "Suggestions"}
                    </h4>
                    {suggestions.map((suggestion, index) => (
                      <div
                        key={`suggestion-${index}`}
                        className="flex items-center justify-between px-3 py-2 hover:bg-gray-50 rounded"
                      >
                        <button
                          onClick={() => handleSuggestionSelect(suggestion)}
                          className="flex-1 text-left text-sm text-gray-700"
                        >
                          {typeof suggestion === "string"
                            ? suggestion
                            : suggestion.name}
                        </button>

                        {typeof suggestion === "object" && suggestion.id && (
                          <button
                            onClick={(e) => handleAddToList(suggestion, e)}
                            className="ml-2 px-2 py-1 text-xs bg-primary-600 text-white rounded hover:bg-primary-700 transition-colors"
                            title="Add to shopping list"
                          >
                            +
                          </button>
                        )}
                      </div>
                    ))}
                  </div>
                )}

                {/* Recent Searches */}
                {recentSearches.length > 0 && (
                  <div className="p-3 border-b border-gray-100">
                    <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">
                      Recent Searches
                    </h4>
                    {recentSearches.slice(0, 5).map((search, index) => (
                      <button
                        key={`recent-${index}`}
                        onClick={() => handleSuggestionSelect(search)}
                        className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded"
                      >
                        {search}
                      </button>
                    ))}
                  </div>
                )}

                {/* Popular Searches */}
                {popularSearchTerms.length > 0 && (
                  <div className="p-3">
                    <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">
                      Popular Searches
                    </h4>
                    {popularSearchTerms.slice(0, 5).map((search, index) => (
                      <button
                        key={`popular-${index}`}
                        onClick={() => handleSuggestionSelect(search)}
                        className="w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded"
                      >
                        {search}
                      </button>
                    ))}
                  </div>
                )}
              </div>
            )}
        </div>

        {/* Search Button */}
        <div className="flex justify-center mt-6">
          <button
            onClick={() => localQuery.trim() && performSearch(localQuery)}
            className="inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
          >
            <Search className="h-5 w-5 mr-2" />
            {hasFilters ? "Search with Filters" : "Search Products"}
          </button>
        </div>
      </div>

      {/* In-Page Product Details */}
      {showProductDetails && selectedProductName && (
        <div className="mt-8">
          <ErrorBoundary>
            <InPageProductDetails
              productName={selectedProductName}
              activeFilters={{
                category: selectedCategories[0],
                manufactured_by: selectedManufacturers[0],
                seller: selectedSellers[0],
              }}
              onClose={handleCloseProductDetails}
            />
          </ErrorBoundary>
        </div>
      )}
    </div>
  );
};

export default UnifiedSearchInterface;
