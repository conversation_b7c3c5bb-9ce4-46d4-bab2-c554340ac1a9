# Page snapshot

```yaml
- text: "[plugin:vite:import-analysis] Failed to resolve import \"../hooks/useNetworkStatus\" from \"src\\pages\\ShoppingListsPage.tsx\". Does the file exist? D:/online work/profident/frontend/src/pages/ShoppingListsPage.tsx:3:33 18 | import { useState } from \"react\"; 19 | import { useNavigate } from \"react-router-dom\"; 20 | import { useNetworkStatus } from \"../hooks/useNetworkStatus\"; | ^ 21 | import { Plus, ShoppingCart, Calendar, Package, X, Edit2 } from \"lucide-react\"; 22 | import { at formatError (file:///D:/online%20work/profident/frontend/node_modules/vite/dist/node/chunks/dep-827b23df.js:44066:46) at TransformContext.error (file:///D:/online%20work/profident/frontend/node_modules/vite/dist/node/chunks/dep-827b23df.js:44062:19) at normalizeUrl (file:///D:/online%20work/profident/frontend/node_modules/vite/dist/node/chunks/dep-827b23df.js:41845:33) at process.processTicksAndRejections (node:internal/process/task_queues:105:5) at async file:///D:/online%20work/profident/frontend/node_modules/vite/dist/node/chunks/dep-827b23df.js:41999:47 at async Promise.all (index 5) at async TransformContext.transform (file:///D:/online%20work/profident/frontend/node_modules/vite/dist/node/chunks/dep-827b23df.js:41915:13) at async Object.transform (file:///D:/online%20work/profident/frontend/node_modules/vite/dist/node/chunks/dep-827b23df.js:44356:30) at async loadAndTransform (file:///D:/online%20work/profident/frontend/node_modules/vite/dist/node/chunks/dep-827b23df.js:55088:29) at async viteTransformMiddleware (file:///D:/online%20work/profident/frontend/node_modules/vite/dist/node/chunks/dep-827b23df.js:64699:32 Click outside, press Esc key, or fix the code to dismiss. You can also disable this overlay by setting"
- code: server.hmr.overlay
- text: to
- code: "false"
- text: in
- code: vite.config.js.
```