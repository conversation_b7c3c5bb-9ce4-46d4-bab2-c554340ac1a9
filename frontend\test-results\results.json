{"config": {"configFile": "D:\\online work\\profident\\frontend\\playwright.config.ts", "rootDir": "D:/online work/profident/frontend/tests", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 6}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "test-results/results.json"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "D:/online work/profident/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "chromium", "name": "chromium", "testDir": "D:/online work/profident/frontend/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/online work/profident/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "firefox", "name": "firefox", "testDir": "D:/online work/profident/frontend/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/online work/profident/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "webkit", "name": "webkit", "testDir": "D:/online work/profident/frontend/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/online work/profident/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "D:/online work/profident/frontend/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "D:/online work/profident/frontend/test-results", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 6}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "D:/online work/profident/frontend/tests", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.1", "workers": 6, "webServer": {"command": "npm run dev", "url": "http://localhost:3000", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "search-functionality.spec.ts", "file": "search-functionality.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Search Functionality Tests", "file": "search-functionality.spec.ts", "line": 3, "column": 6, "specs": [{"title": "should display search suggestions when typing", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 10276, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n\n    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:17:31", "location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 17}, "snippet": "\u001b[0m \u001b[90m 15 |\u001b[39m     \u001b[90m// Find the search input\u001b[39m\n \u001b[90m 16 |\u001b[39m     \u001b[36mconst\u001b[39m searchInput \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder*=\"Search\"]'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 17 |\u001b[39m     \u001b[36mawait\u001b[39m expect(searchInput)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 18 |\u001b[39m     \n \u001b[90m 19 |\u001b[39m     \u001b[90m// Type in the search input\u001b[39m\n \u001b[90m 20 |\u001b[39m     \u001b[36mawait\u001b[39m searchInput\u001b[33m.\u001b[39mfill(\u001b[32m'dental'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 17}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n\n\n\u001b[0m \u001b[90m 15 |\u001b[39m     \u001b[90m// Find the search input\u001b[39m\n \u001b[90m 16 |\u001b[39m     \u001b[36mconst\u001b[39m searchInput \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder*=\"Search\"]'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 17 |\u001b[39m     \u001b[36mawait\u001b[39m expect(searchInput)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 18 |\u001b[39m     \n \u001b[90m 19 |\u001b[39m     \u001b[90m// Type in the search input\u001b[39m\n \u001b[90m 20 |\u001b[39m     \u001b[36mawait\u001b[39m searchInput\u001b[33m.\u001b[39mfill(\u001b[32m'dental'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:17:31\u001b[22m"}], "stdout": [{"text": "Testing search suggestions...\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-31T18:50:53.145Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-78a37-rch-suggestions-when-typing-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-78a37-rch-suggestions-when-typing-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-78a37-rch-suggestions-when-typing-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 17}}], "status": "unexpected"}], "id": "b40160f6fba3e7a72973-6143670eb0499cf1761d", "file": "search-functionality.spec.ts", "line": 12, "column": 3}, {"title": "should perform search and show results", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 10787, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n\n    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:45:31", "location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 45}, "snippet": "\u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[90m// Find the search input\u001b[39m\n \u001b[90m 44 |\u001b[39m     \u001b[36mconst\u001b[39m searchInput \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder*=\"Search\"]'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 45 |\u001b[39m     \u001b[36mawait\u001b[39m expect(searchInput)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 46 |\u001b[39m     \n \u001b[90m 47 |\u001b[39m     \u001b[90m// Type and press Enter\u001b[39m\n \u001b[90m 48 |\u001b[39m     \u001b[36mawait\u001b[39m searchInput\u001b[33m.\u001b[39mfill(\u001b[32m'dental'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 45}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n\n\n\u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[90m// Find the search input\u001b[39m\n \u001b[90m 44 |\u001b[39m     \u001b[36mconst\u001b[39m searchInput \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder*=\"Search\"]'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 45 |\u001b[39m     \u001b[36mawait\u001b[39m expect(searchInput)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 46 |\u001b[39m     \n \u001b[90m 47 |\u001b[39m     \u001b[90m// Type and press Enter\u001b[39m\n \u001b[90m 48 |\u001b[39m     \u001b[36mawait\u001b[39m searchInput\u001b[33m.\u001b[39mfill(\u001b[32m'dental'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:45:31\u001b[22m"}], "stdout": [{"text": "Testing search results...\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-31T18:50:53.183Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-68681-orm-search-and-show-results-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-68681-orm-search-and-show-results-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-68681-orm-search-and-show-results-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 45}}], "status": "unexpected"}], "id": "b40160f6fba3e7a72973-7692931df95cef6df7e2", "file": "search-functionality.spec.ts", "line": 40, "column": 3}, {"title": "should test API endpoints directly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "passed", "duration": 6628, "errors": [], "stdout": [{"text": "Testing API endpoints...\n"}, {"text": "Suggestions API response: {\n  success: \u001b[33mtrue\u001b[39m,\n  message: \u001b[32m'Suggestions retrieved successfully'\u001b[39m,\n  query: \u001b[32m'dental'\u001b[39m,\n  suggestions: [\n    {\n      id: \u001b[33m26974\u001b[39m,\n      mfr: \u001b[32m'77023-1'\u001b[39m,\n      name: \u001b[32m'Seal-Tight® Auto-Lock Adapter Type \"D\" Dental-Ez'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/3143-117/seal-tight-auto-lock-adapter-type-d-dental-ez'\u001b[39m,\n      maincat: \u001b[32m'Disposables'\u001b[39m,\n      brand: \u001b[32m'Kerr TotalCare'\u001b[39m,\n      manufactured_by: \u001b[32m'kavo-kerr'\u001b[39m,\n      category: \u001b[32m'Disposables & Infection Control'\u001b[39m\n    },\n    {\n      id: \u001b[33m119535\u001b[39m,\n      mfr: \u001b[32m'31009-2'\u001b[39m,\n      name: \u001b[32m'Chairmount Dental-Eze NuSimplicity'\u001b[39m,\n      seller: \u001b[32m'dds'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://ddsdentalsupplies.com/chairmount-dental-eze-nusimplicity/'\u001b[39m,\n      maincat: \u001b[32m'Miscellaneous'\u001b[39m,\n      brand: \u001b[32m'Crosstex International'\u001b[39m,\n      manufactured_by: \u001b[32m'crosstex'\u001b[39m,\n      category: \u001b[32m'Miscellaneous'\u001b[39m\n    },\n    {\n      id: \u001b[33m119536\u001b[39m,\n      mfr: \u001b[32m'31009-1'\u001b[39m,\n      name: \u001b[32m'Chairmount Dental-Eze Simplicity'\u001b[39m,\n      seller: \u001b[32m'dds'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://ddsdentalsupplies.com/chairmount-dental-eze-simplicity/'\u001b[39m,\n      maincat: \u001b[32m'Miscellaneous'\u001b[39m,\n      brand: \u001b[32m'Crosstex International'\u001b[39m,\n      manufactured_by: \u001b[32m'crosstex'\u001b[39m,\n      category: \u001b[32m'Miscellaneous'\u001b[39m\n    },\n    {\n      id: \u001b[33m262197\u001b[39m,\n      mfr: \u001b[32m'FGSS557'\u001b[39m,\n      name: \u001b[32m'DentalRee Dental Ree FG #557 SS (Short Shank) Straight Fissure Crosscut Carbide Bur, 10/Pk'\u001b[39m,\n      seller: \u001b[32m'net32'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://www.net32.com/ec/dentalree-dental-ree-fg-557-ss-short-d-181969?queryID=bb4766df2db464254b5a30d4dc456052'\u001b[39m,\n      maincat: \u001b[32m'Burs & diamonds'\u001b[39m,\n      brand: \u001b[32m'DentalRee'\u001b[39m,\n      manufactured_by: \u001b[32m'dentalree'\u001b[39m,\n      category: \u001b[32m'Rotary Instruments (Burs, Diamonds, Polishers)'\u001b[39m\n    },\n    {\n      id: \u001b[33m263274\u001b[39m,\n      mfr: \u001b[32m'FGSS57'\u001b[39m,\n      name: \u001b[32m'DentalRee Dental Ree FG #57 SS (Short Shank) Straight Fissure Carbide Bur, 10/Pk'\u001b[39m,\n      seller: \u001b[32m'net32'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://www.net32.com/ec/dentalree-dental-ree-fg-57-ss-short-d-181970?queryID=bd26415c286636703f53fa6eec443455'\u001b[39m,\n      maincat: \u001b[32m'Burs & diamonds'\u001b[39m,\n      brand: \u001b[32m'DentalRee'\u001b[39m,\n      manufactured_by: \u001b[32m'dentalree'\u001b[39m,\n      category: \u001b[32m'Rotary Instruments (Burs, Diamonds, Polishers)'\u001b[39m\n    },\n    {\n      id: \u001b[33m283631\u001b[39m,\n      mfr: \u001b[32m'BB030'\u001b[39m,\n      name: \u001b[32m'Block-4-Bite Disposable X-Ray Bite Blocks, Fits Phillips, Dental-Ez Panolite'\u001b[39m,\n      seller: \u001b[32m'net32'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://www.net32.com/ec/block4bite-disposable-xray-bite-blocks-fits-phillips-d-54856?queryID=56125ad20d6ec0db166024722df5de5c'\u001b[39m,\n      maincat: \u001b[32m'X-Ray products'\u001b[39m,\n      brand: \u001b[32m'Plasdent'\u001b[39m,\n      manufactured_by: \u001b[32m'plasdent'\u001b[39m,\n      category: \u001b[32m'Diagnostic Tools & Imaging'\u001b[39m\n    },\n    {\n      id: \u001b[33m332360\u001b[39m,\n      mfr: \u001b[32m'261547'\u001b[39m,\n      name: \u001b[32m'Dental-EZ Handpiece Swivels, 4-Line'\u001b[39m,\n      seller: \u001b[32m'tdsc'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://www.tdsc.com/All-Categories/Dental-Supplies/Handpieces/Handpiece-Tools-%26-Maintenance/Handpiece-Swivels/Dental-EZ-Handpiece-Swivels/p/220396-1?q=&productListName=Category%20Page&dimension8=&pageRef=CATEGORY'\u001b[39m,\n      maincat: \u001b[32m'Handpieces'\u001b[39m,\n      brand: \u001b[32m'STARDENTAL-DENTAL EZ'\u001b[39m,\n      manufactured_by: \u001b[32m'dentalez'\u001b[39m,\n      category: \u001b[32m'Rotary Instruments (Burs, Diamonds, Polishers)'\u001b[39m\n    },\n    {\n      id: \u001b[33m22003\u001b[39m,\n      mfr: \u001b[32m'5133-026'\u001b[39m,\n      name: \u001b[32m'Benco Dental™ Dental Disperse Tip 19GA Yellow Pack of 20'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/5133-026/benco-dental-dental-disperse-tip-19ga-yellow-pack-'\u001b[39m,\n      maincat: \u001b[32m'Cosmetic Dentistry'\u001b[39m,\n      brand: \u001b[32m'BENCO'\u001b[39m,\n      manufactured_by: \u001b[32m'benco'\u001b[39m,\n      category: \u001b[32m'Restorative Materials (Composites, Amalgams, Glass Ionomers)'\u001b[39m\n    },\n    {\n      id: \u001b[33m22004\u001b[39m,\n      mfr: \u001b[32m'5133-035'\u001b[39m,\n      name: \u001b[32m'Benco Dental™ Dental Disperse Tip 19GA Yellow Pack of 100'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/5133-035/benco-dental-dental-disperse-tip-19ga-yellow-pack-'\u001b[39m,\n      maincat: \u001b[32m'Cosmetic Dentistry'\u001b[39m,\n      brand: \u001b[32m'BENCO'\u001b[39m,\n      manufactured_by: \u001b[32m'benco'\u001b[39m,\n      category: \u001b[32m'Restorative Materials (Composites, Amalgams, Glass Ionomers)'\u001b[39m\n    },\n    {\n      id: \u001b[33m25910\u001b[39m,\n      mfr: \u001b[32m'3229-927'\u001b[39m,\n      name: \u001b[32m'Benco Dental™ Blue Thin Rubber Latex Dental Dam 5\"x5\" Box of 52'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/3229-927/benco-dental-blue-thin-rubber-latex-dental-dam-5x5'\u001b[39m,\n      maincat: \u001b[32m'Dental Dam'\u001b[39m,\n      brand: \u001b[32m'BENCO'\u001b[39m,\n      manufactured_by: \u001b[32m'benco'\u001b[39m,\n      category: \u001b[32m'Disposables & Infection Control'\u001b[39m\n    }\n  ]\n}\n"}, {"text": "Search API response: {\n  success: \u001b[33mtrue\u001b[39m,\n  message: \u001b[32m'Search completed successfully (cached)'\u001b[39m,\n  query: \u001b[32m'dental'\u001b[39m,\n  search_type: \u001b[32m'fulltext'\u001b[39m,\n  total: \u001b[33m5810\u001b[39m,\n  results: [\n    {\n      id: \u001b[33m26974\u001b[39m,\n      mfr: \u001b[32m'77023-1'\u001b[39m,\n      name: \u001b[32m'Seal-Tight® Auto-Lock Adapter Type \"D\" Dental-Ez'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/3143-117/seal-tight-auto-lock-adapter-type-d-dental-ez'\u001b[39m,\n      maincat: \u001b[32m'Disposables'\u001b[39m,\n      brand: \u001b[32m'Kerr TotalCare'\u001b[39m,\n      manufactured_by: \u001b[32m'kavo-kerr'\u001b[39m,\n      category: \u001b[32m'Disposables & Infection Control'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'77023-1 | Seal-Tight® Auto-Lock Adapter Type \"D\" Dental-Ez'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m119535\u001b[39m,\n      mfr: \u001b[32m'31009-2'\u001b[39m,\n      name: \u001b[32m'Chairmount Dental-Eze NuSimplicity'\u001b[39m,\n      url: \u001b[32m'https://ddsdentalsupplies.com/chairmount-dental-eze-nusimplicity/'\u001b[39m,\n      maincat: \u001b[32m'Miscellaneous'\u001b[39m,\n      brand: \u001b[32m'Crosstex International'\u001b[39m,\n      manufactured_by: \u001b[32m'crosstex'\u001b[39m,\n      category: \u001b[32m'Miscellaneous'\u001b[39m,\n      seller: \u001b[32m'dds'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'31009-2 | Chairmount Dental-Eze NuSimplicity'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m119536\u001b[39m,\n      mfr: \u001b[32m'31009-1'\u001b[39m,\n      name: \u001b[32m'Chairmount Dental-Eze Simplicity'\u001b[39m,\n      url: \u001b[32m'https://ddsdentalsupplies.com/chairmount-dental-eze-simplicity/'\u001b[39m,\n      maincat: \u001b[32m'Miscellaneous'\u001b[39m,\n      brand: \u001b[32m'Crosstex International'\u001b[39m,\n      manufactured_by: \u001b[32m'crosstex'\u001b[39m,\n      category: \u001b[32m'Miscellaneous'\u001b[39m,\n      seller: \u001b[32m'dds'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'31009-1 | Chairmount Dental-Eze Simplicity'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m262197\u001b[39m,\n      mfr: \u001b[32m'FGSS557'\u001b[39m,\n      name: \u001b[32m'DentalRee Dental Ree FG #557 SS (Short Shank) Straight Fissure Crosscut Carbide Bur, 10/Pk'\u001b[39m,\n      url: \u001b[32m'https://www.net32.com/ec/dentalree-dental-ree-fg-557-ss-short-d-181969?queryID=bb4766df2db464254b5a30d4dc456052'\u001b[39m,\n      maincat: \u001b[32m'Burs & diamonds'\u001b[39m,\n      brand: \u001b[32m'DentalRee'\u001b[39m,\n      manufactured_by: \u001b[32m'dentalree'\u001b[39m,\n      category: \u001b[32m'Rotary Instruments (Burs, Diamonds, Polishers)'\u001b[39m,\n      seller: \u001b[32m'net32'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'FGSS557 | DentalRee Dental Ree FG #557 SS (Short Shank) Straight Fissure Crosscut Carbide Bur, 10/Pk'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m263274\u001b[39m,\n      mfr: \u001b[32m'FGSS57'\u001b[39m,\n      name: \u001b[32m'DentalRee Dental Ree FG #57 SS (Short Shank) Straight Fissure Carbide Bur, 10/Pk'\u001b[39m,\n      url: \u001b[32m'https://www.net32.com/ec/dentalree-dental-ree-fg-57-ss-short-d-181970?queryID=bd26415c286636703f53fa6eec443455'\u001b[39m,\n      maincat: \u001b[32m'Burs & diamonds'\u001b[39m,\n      brand: \u001b[32m'DentalRee'\u001b[39m,\n      manufactured_by: \u001b[32m'dentalree'\u001b[39m,\n      category: \u001b[32m'Rotary Instruments (Burs, Diamonds, Polishers)'\u001b[39m,\n      seller: \u001b[32m'net32'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'FGSS57 | DentalRee Dental Ree FG #57 SS (Short Shank) Straight Fissure Carbide Bur, 10/Pk'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m283631\u001b[39m,\n      mfr: \u001b[32m'BB030'\u001b[39m,\n      name: \u001b[32m'Block-4-Bite Disposable X-Ray Bite Blocks, Fits Phillips, Dental-Ez Panolite'\u001b[39m,\n      url: \u001b[32m'https://www.net32.com/ec/block4bite-disposable-xray-bite-blocks-fits-phillips-d-54856?queryID=56125ad20d6ec0db166024722df5de5c'\u001b[39m,\n      maincat: \u001b[32m'X-Ray products'\u001b[39m,\n      brand: \u001b[32m'Plasdent'\u001b[39m,\n      manufactured_by: \u001b[32m'plasdent'\u001b[39m,\n      category: \u001b[32m'Diagnostic Tools & Imaging'\u001b[39m,\n      seller: \u001b[32m'net32'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'BB030 | Block-4-Bite Disposable X-Ray Bite Blocks, Fits Phillips, Dental-Ez Panolite'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m332360\u001b[39m,\n      mfr: \u001b[32m'261547'\u001b[39m,\n      name: \u001b[32m'Dental-EZ Handpiece Swivels, 4-Line'\u001b[39m,\n      url: \u001b[32m'https://www.tdsc.com/All-Categories/Dental-Supplies/Handpieces/Handpiece-Tools-%26-Maintenance/Handpiece-Swivels/Dental-EZ-Handpiece-Swivels/p/220396-1?q=&productListName=Category%20Page&dimension8=&pageRef=CATEGORY'\u001b[39m,\n      maincat: \u001b[32m'Handpieces'\u001b[39m,\n      brand: \u001b[32m'STARDENTAL-DENTAL EZ'\u001b[39m,\n      manufactured_by: \u001b[32m'dentalez'\u001b[39m,\n      category: \u001b[32m'Rotary Instruments (Burs, Diamonds, Polishers)'\u001b[39m,\n      seller: \u001b[32m'tdsc'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'261547 | Dental-EZ Handpiece Swivels, 4-Line'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m22003\u001b[39m,\n      mfr: \u001b[32m'5133-026'\u001b[39m,\n      name: \u001b[32m'Benco Dental™ Dental Disperse Tip 19GA Yellow Pack of 20'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/5133-026/benco-dental-dental-disperse-tip-19ga-yellow-pack-'\u001b[39m,\n      maincat: \u001b[32m'Cosmetic Dentistry'\u001b[39m,\n      brand: \u001b[32m'BENCO'\u001b[39m,\n      manufactured_by: \u001b[32m'benco'\u001b[39m,\n      category: \u001b[32m'Restorative Materials (Composites, Amalgams, Glass Ionomers)'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'5133-026 | Benco Dental™ Dental Disperse Tip 19GA Yellow Pack of 20'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m22004\u001b[39m,\n      mfr: \u001b[32m'5133-035'\u001b[39m,\n      name: \u001b[32m'Benco Dental™ Dental Disperse Tip 19GA Yellow Pack of 100'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/5133-035/benco-dental-dental-disperse-tip-19ga-yellow-pack-'\u001b[39m,\n      maincat: \u001b[32m'Cosmetic Dentistry'\u001b[39m,\n      brand: \u001b[32m'BENCO'\u001b[39m,\n      manufactured_by: \u001b[32m'benco'\u001b[39m,\n      category: \u001b[32m'Restorative Materials (Composites, Amalgams, Glass Ionomers)'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'5133-035 | Benco Dental™ Dental Disperse Tip 19GA Yellow Pack of 100'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m25910\u001b[39m,\n      mfr: \u001b[32m'3229-927'\u001b[39m,\n      name: \u001b[32m'Benco Dental™ Blue Thin Rubber Latex Dental Dam 5\"x5\" Box of 52'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/3229-927/benco-dental-blue-thin-rubber-latex-dental-dam-5x5'\u001b[39m,\n      maincat: \u001b[32m'Dental Dam'\u001b[39m,\n      brand: \u001b[32m'BENCO'\u001b[39m,\n      manufactured_by: \u001b[32m'benco'\u001b[39m,\n      category: \u001b[32m'Disposables & Infection Control'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'3229-927 | Benco Dental™ Blue Thin Rubber Latex Dental Dam 5\"x5\" Box of 52'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    }\n  ],\n  pagination: {\n    total: \u001b[33m5810\u001b[39m,\n    page: \u001b[33m1\u001b[39m,\n    per_page: \u001b[33m10\u001b[39m,\n    total_pages: \u001b[33m581\u001b[39m,\n    has_next: \u001b[33mtrue\u001b[39m,\n    has_prev: \u001b[33mfalse\u001b[39m\n  },\n  filters_applied: \u001b[1mnull\u001b[22m,\n  search_time_ms: \u001b[33m1.9927024841308594\u001b[39m\n}\n"}, {"text": "Popular searches API response: {\n  success: \u001b[33mtrue\u001b[39m,\n  message: \u001b[32m'Popular searches retrieved successfully'\u001b[39m,\n  popular_searches: [\n    { term: \u001b[32m'Instruments'\u001b[39m, count: \u001b[33m36356\u001b[39m },\n    { term: \u001b[32m'Endodontics'\u001b[39m, count: \u001b[33m21769\u001b[39m },\n    { term: \u001b[32m'Burs & Diamonds'\u001b[39m, count: \u001b[33m19053\u001b[39m },\n    { term: \u001b[32m'Infection Control'\u001b[39m, count: \u001b[33m15785\u001b[39m },\n    { term: \u001b[32m'Hu-<PERSON><PERSON><PERSON>'\u001b[39m, count: \u001b[33m14752\u001b[39m }\n  ]\n}\n"}, {"text": "API endpoints test completed successfully\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-31T18:50:53.149Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "b40160f6fba3e7a72973-2b9c7f1f5d87bc231c57", "file": "search-functionality.spec.ts", "line": 70, "column": 3}, {"title": "should test filter functionality", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 5446, "errors": [], "stdout": [{"text": "Testing filter functionality...\n"}, {"text": "Categories API response: {\n  success: \u001b[33mtrue\u001b[39m,\n  message: \u001b[32m'Categories retrieved successfully'\u001b[39m,\n  categories: [\n    {\n      category: \u001b[32m'Rotary Instruments (Burs, Diamonds, Polishers)'\u001b[39m,\n      product_count: \u001b[33m49726\u001b[39m\n    },\n    { category: \u001b[32m'Instruments (Hand)'\u001b[39m, product_count: \u001b[33m42762\u001b[39m },\n    {\n      category: \u001b[32m'Disposables & Infection Control'\u001b[39m,\n      product_count: \u001b[33m36221\u001b[39m\n    },\n    {\n      category: \u001b[32m'Restorative Materials (Composites, Amalgams, Glass Ionomers)'\u001b[39m,\n      product_count: \u001b[33m34559\u001b[39m\n    },\n    { category: \u001b[32m'Endodontics'\u001b[39m, product_count: \u001b[33m26829\u001b[39m },\n    { category: \u001b[32m'Laboratory Products'\u001b[39m, product_count: \u001b[33m24842\u001b[39m },\n    { category: \u001b[32m'Equipment (General)'\u001b[39m, product_count: \u001b[33m17145\u001b[39m },\n    { category: \u001b[32m'Orthodontics'\u001b[39m, product_count: \u001b[33m14315\u001b[39m },\n    { category: \u001b[32m'Preventative & Oral Hygiene'\u001b[39m, product_count: \u001b[33m14299\u001b[39m },\n    {\n      category: \u001b[32m'Prosthodontics (Crowns, Bridges, Dentures)'\u001b[39m,\n      product_count: \u001b[33m13344\u001b[39m\n    },\n    { category: \u001b[32m'Miscellaneous'\u001b[39m, product_count: \u001b[33m13308\u001b[39m },\n    { category: \u001b[32m'Surgical Supplies'\u001b[39m, product_count: \u001b[33m13292\u001b[39m },\n    { category: \u001b[32m'Impression Materials'\u001b[39m, product_count: \u001b[33m11897\u001b[39m },\n    { category: \u001b[32m'Finishing & Polishing'\u001b[39m, product_count: \u001b[33m9048\u001b[39m },\n    { category: \u001b[32m'Diagnostic Tools & Imaging'\u001b[39m, product_count: \u001b[33m6099\u001b[39m },\n    { category: \u001b[32m'Anesthetics & Pain Management'\u001b[39m, product_count: \u001b[33m6041\u001b[39m },\n    { category: \u001b[32m'Cements & Liners'\u001b[39m, product_count: \u001b[33m3207\u001b[39m },\n    { category: \u001b[32m'Bonding & Adhesives'\u001b[39m, product_count: \u001b[33m1217\u001b[39m },\n    { category: \u001b[32m'Waxes & Modeling Materials'\u001b[39m, product_count: \u001b[33m761\u001b[39m },\n    { category: \u001b[32m'Curing Lights'\u001b[39m, product_count: \u001b[33m166\u001b[39m }\n  ]\n}\n"}, {"text": "Manufacturers API response: {\n  success: \u001b[33mtrue\u001b[39m,\n  message: \u001b[32m'Manufacturers retrieved successfully'\u001b[39m,\n  manufacturers: [\n    { manufacturer: \u001b[32m'kavo-kerr'\u001b[39m, product_count: \u001b[33m25380\u001b[39m },\n    { manufacturer: \u001b[32m'hu-friedy'\u001b[39m, product_count: \u001b[33m17549\u001b[39m },\n    { manufacturer: \u001b[32m'dentsply-sirona'\u001b[39m, product_count: \u001b[33m14532\u001b[39m },\n    { manufacturer: \u001b[32m'henry-schein'\u001b[39m, product_count: \u001b[33m11349\u001b[39m },\n    { manufacturer: \u001b[32m'coltene-whaledent'\u001b[39m, product_count: \u001b[33m9412\u001b[39m },\n    { manufacturer: \u001b[32m'gc-america'\u001b[39m, product_count: \u001b[33m7535\u001b[39m },\n    { manufacturer: \u001b[32m'3m-solventum'\u001b[39m, product_count: \u001b[33m7163\u001b[39m },\n    { manufacturer: \u001b[32m'premier-dental'\u001b[39m, product_count: \u001b[33m7108\u001b[39m },\n    { manufacturer: \u001b[32m'kulzer'\u001b[39m, product_count: \u001b[33m6394\u001b[39m },\n    { manufacturer: \u001b[32m'ss-white'\u001b[39m, product_count: \u001b[33m6060\u001b[39m },\n    { manufacturer: \u001b[32m'keystone-industries'\u001b[39m, product_count: \u001b[33m5532\u001b[39m },\n    { manufacturer: \u001b[32m'belmont'\u001b[39m, product_count: \u001b[33m5113\u001b[39m },\n    { manufacturer: \u001b[32m'meisinger'\u001b[39m, product_count: \u001b[33m4947\u001b[39m },\n    { manufacturer: \u001b[32m'ivoclar-vivadent'\u001b[39m, product_count: \u001b[33m4642\u001b[39m },\n    { manufacturer: \u001b[32m'shofu'\u001b[39m, product_count: \u001b[33m4580\u001b[39m },\n    { manufacturer: \u001b[32m'nordent'\u001b[39m, product_count: \u001b[33m4402\u001b[39m },\n    { manufacturer: \u001b[32m'pac-dent'\u001b[39m, product_count: \u001b[33m4164\u001b[39m },\n    { manufacturer: \u001b[32m'zirc'\u001b[39m, product_count: \u001b[33m3856\u001b[39m },\n    { manufacturer: \u001b[32m'integra-miltex'\u001b[39m, product_count: \u001b[33m3802\u001b[39m },\n    { manufacturer: \u001b[32m'benco'\u001b[39m, product_count: \u001b[33m3607\u001b[39m },\n    { manufacturer: \u001b[32m'crosstex'\u001b[39m, product_count: \u001b[33m3346\u001b[39m },\n    { manufacturer: \u001b[32m'darby'\u001b[39m, product_count: \u001b[33m3198\u001b[39m },\n    { manufacturer: \u001b[32m'american-eagle'\u001b[39m, product_count: \u001b[33m2893\u001b[39m },\n    { manufacturer: \u001b[32m'ortho-technology'\u001b[39m, product_count: \u001b[33m2848\u001b[39m },\n    { manufacturer: \u001b[32m'ortho-essentials'\u001b[39m, product_count: \u001b[33m2779\u001b[39m },\n    { manufacturer: \u001b[32m'quala'\u001b[39m, product_count: \u001b[33m2650\u001b[39m },\n    { manufacturer: \u001b[32m'kuraray'\u001b[39m, product_count: \u001b[33m2483\u001b[39m },\n    { manufacturer: \u001b[32m'a-titan'\u001b[39m, product_count: \u001b[33m2455\u001b[39m },\n    { manufacturer: \u001b[32m'ortho-organizers'\u001b[39m, product_count: \u001b[33m2431\u001b[39m },\n    { manufacturer: \u001b[32m'plasdent'\u001b[39m, product_count: \u001b[33m2249\u001b[39m },\n    { manufacturer: \u001b[32m'dentalez'\u001b[39m, product_count: \u001b[33m2236\u001b[39m },\n    { manufacturer: \u001b[32m'young-dental'\u001b[39m, product_count: \u001b[33m2180\u001b[39m },\n    { manufacturer: \u001b[32m'amann-girrbach'\u001b[39m, product_count: \u001b[33m2173\u001b[39m },\n    { manufacturer: \u001b[32m'advance'\u001b[39m, product_count: \u001b[33m2125\u001b[39m },\n    { manufacturer: \u001b[32m'sdi'\u001b[39m, product_count: \u001b[33m2125\u001b[39m },\n    { manufacturer: \u001b[32m'directa'\u001b[39m, product_count: \u001b[33m2070\u001b[39m },\n    { manufacturer: \u001b[32m'palmero'\u001b[39m, product_count: \u001b[33m1994\u001b[39m },\n    { manufacturer: \u001b[32m'medicom'\u001b[39m, product_count: \u001b[33m1953\u001b[39m },\n    { manufacturer: \u001b[32m'diadent'\u001b[39m, product_count: \u001b[33m1858\u001b[39m },\n    { manufacturer: \u001b[32m'voco'\u001b[39m, product_count: \u001b[33m1848\u001b[39m },\n    { manufacturer: \u001b[32m'vista-apex'\u001b[39m, product_count: \u001b[33m1786\u001b[39m },\n    { manufacturer: \u001b[32m'dedeco'\u001b[39m, product_count: \u001b[33m1715\u001b[39m },\n    { manufacturer: \u001b[32m'buffalo-dental'\u001b[39m, product_count: \u001b[33m1711\u001b[39m },\n    { manufacturer: \u001b[32m'safco-dental'\u001b[39m, product_count: \u001b[33m1700\u001b[39m },\n    { manufacturer: \u001b[32m'superior-uniform'\u001b[39m, product_count: \u001b[33m1700\u001b[39m },\n    { manufacturer: \u001b[32m'3b-orthodontics'\u001b[39m, product_count: \u001b[33m1669\u001b[39m },\n    { manufacturer: \u001b[32m'pulpdent'\u001b[39m, product_count: \u001b[33m1644\u001b[39m },\n    { manufacturer: \u001b[32m'microcopy'\u001b[39m, product_count: \u001b[33m1643\u001b[39m },\n    { manufacturer: \u001b[32m'wonderwink'\u001b[39m, product_count: \u001b[33m1583\u001b[39m },\n    { manufacturer: \u001b[32m'j-j-instruments'\u001b[39m, product_count: \u001b[33m1560\u001b[39m },\n    { manufacturer: \u001b[32m'dmg-america'\u001b[39m, product_count: \u001b[33m1543\u001b[39m },\n    { manufacturer: \u001b[32m'edge-endo'\u001b[39m, product_count: \u001b[33m1522\u001b[39m },\n    { manufacturer: \u001b[32m'midmark'\u001b[39m, product_count: \u001b[33m1511\u001b[39m },\n    { manufacturer: \u001b[32m'parkell'\u001b[39m, product_count: \u001b[33m1502\u001b[39m },\n    { manufacturer: \u001b[32m'aidusa'\u001b[39m, product_count: \u001b[33m1463\u001b[39m },\n    { manufacturer: \u001b[32m'denmat'\u001b[39m, product_count: \u001b[33m1456\u001b[39m },\n    { manufacturer: \u001b[32m'ansell'\u001b[39m, product_count: \u001b[33m1424\u001b[39m },\n    { manufacturer: \u001b[32m'valumax'\u001b[39m, product_count: \u001b[33m1368\u001b[39m },\n    { manufacturer: \u001b[32m'garrison-dental'\u001b[39m, product_count: \u001b[33m1349\u001b[39m },\n    { manufacturer: \u001b[32m'vita'\u001b[39m, product_count: \u001b[33m1302\u001b[39m },\n    { manufacturer: \u001b[32m'cargus'\u001b[39m, product_count: \u001b[33m1293\u001b[39m },\n    { manufacturer: \u001b[32m'lang-dental'\u001b[39m, product_count: \u001b[33m1218\u001b[39m },\n    { manufacturer: \u001b[32m'house-brand'\u001b[39m, product_count: \u001b[33m1159\u001b[39m },\n    { manufacturer: \u001b[32m'megagen'\u001b[39m, product_count: \u001b[33m1075\u001b[39m },\n    { manufacturer: \u001b[32m'dukal'\u001b[39m, product_count: \u001b[33m1024\u001b[39m },\n    { manufacturer: \u001b[32m'defend'\u001b[39m, product_count: \u001b[33m1007\u001b[39m },\n    { manufacturer: \u001b[32m'ace-surgical'\u001b[39m, product_count: \u001b[33m989\u001b[39m },\n    { manufacturer: \u001b[32m'danville-materials'\u001b[39m, product_count: \u001b[33m975\u001b[39m },\n    { manufacturer: \u001b[32m'sunstar'\u001b[39m, product_count: \u001b[33m968\u001b[39m },\n    { manufacturer: \u001b[32m'sultan-healthcare'\u001b[39m, product_count: \u001b[33m967\u001b[39m },\n    { manufacturer: \u001b[32m'plak-smacker'\u001b[39m, product_count: \u001b[33m965\u001b[39m },\n    { manufacturer: \u001b[32m'practicon'\u001b[39m, product_count: \u001b[33m960\u001b[39m },\n    { manufacturer: \u001b[32m'centrix'\u001b[39m, product_count: \u001b[33m934\u001b[39m },\n    { manufacturer: \u001b[32m'cardinal-health'\u001b[39m, product_count: \u001b[33m923\u001b[39m },\n    { manufacturer: \u001b[32m'flow-dental'\u001b[39m, product_count: \u001b[33m920\u001b[39m },\n    { manufacturer: \u001b[32m'nsk'\u001b[39m, product_count: \u001b[33m910\u001b[39m },\n    { manufacturer: \u001b[32m'js-dental'\u001b[39m, product_count: \u001b[33m877\u001b[39m },\n    { manufacturer: \u001b[32m'tokuyama'\u001b[39m, product_count: \u001b[33m860\u001b[39m },\n    { manufacturer: \u001b[32m'tidi-products'\u001b[39m, product_count: \u001b[33m854\u001b[39m },\n    { manufacturer: \u001b[32m'roydent'\u001b[39m, product_count: \u001b[33m853\u001b[39m },\n    { manufacturer: \u001b[32m'hager-worldwide'\u001b[39m, product_count: \u001b[33m849\u001b[39m },\n    { manufacturer: \u001b[32m'acteon'\u001b[39m, product_count: \u001b[33m838\u001b[39m },\n    { manufacturer: \u001b[32m'halyard'\u001b[39m, product_count: \u001b[33m831\u001b[39m },\n    { manufacturer: \u001b[32m'clinicians-choice'\u001b[39m, product_count: \u001b[33m827\u001b[39m },\n    { manufacturer: \u001b[32m'smistr'\u001b[39m, product_count: \u001b[33m821\u001b[39m },\n    { manufacturer: \u001b[32m'parker-hannifin'\u001b[39m, product_count: \u001b[33m798\u001b[39m },\n    { manufacturer: \u001b[32m'procter-gamble'\u001b[39m, product_count: \u001b[33m798\u001b[39m },\n    { manufacturer: \u001b[32m'ods'\u001b[39m, product_count: \u001b[33m786\u001b[39m },\n    { manufacturer: \u001b[32m'brasseler'\u001b[39m, product_count: \u001b[33m774\u001b[39m },\n    { manufacturer: \u001b[32m'accutron'\u001b[39m, product_count: \u001b[33m767\u001b[39m },\n    { manufacturer: \u001b[32m'waterpik'\u001b[39m, product_count: \u001b[33m760\u001b[39m },\n    { manufacturer: \u001b[32m'pdt'\u001b[39m, product_count: \u001b[33m734\u001b[39m },\n    { manufacturer: \u001b[32m'paradise-dental'\u001b[39m, product_count: \u001b[33m727\u001b[39m },\n    { manufacturer: \u001b[32m'air-techniques'\u001b[39m, product_count: \u001b[33m706\u001b[39m },\n    { manufacturer: \u001b[32m'dci-international'\u001b[39m, product_count: \u001b[33m701\u001b[39m },\n    { manufacturer: \u001b[32m'spring-health'\u001b[39m, product_count: \u001b[33m700\u001b[39m },\n    { manufacturer: \u001b[32m'sagemax'\u001b[39m, product_count: \u001b[33m684\u001b[39m },\n    { manufacturer: \u001b[32m'essential-dental'\u001b[39m, product_count: \u001b[33m672\u001b[39m },\n    { manufacturer: \u001b[32m'ismile-dental'\u001b[39m, product_count: \u001b[33m671\u001b[39m },\n    { manufacturer: \u001b[32m'acero-crowns'\u001b[39m, product_count: \u001b[33m658\u001b[39m }\n  ]\n}\n"}, {"text": "Sellers API response: {\n  success: \u001b[33mtrue\u001b[39m,\n  message: \u001b[32m'Sellers retrieved successfully'\u001b[39m,\n  sellers: [\n    { seller: \u001b[32m'benco'\u001b[39m, product_count: \u001b[33m82084\u001b[39m },\n    { seller: \u001b[32m'henry'\u001b[39m, product_count: \u001b[33m76867\u001b[39m },\n    { seller: \u001b[32m'midwest'\u001b[39m, product_count: \u001b[33m41946\u001b[39m },\n    { seller: \u001b[32m'darby'\u001b[39m, product_count: \u001b[33m27778\u001b[39m },\n    { seller: \u001b[32m'net32'\u001b[39m, product_count: \u001b[33m26862\u001b[39m },\n    { seller: \u001b[32m'optimus'\u001b[39m, product_count: \u001b[33m20714\u001b[39m },\n    { seller: \u001b[32m'safco'\u001b[39m, product_count: \u001b[33m17669\u001b[39m },\n    { seller: \u001b[32m'dds'\u001b[39m, product_count: \u001b[33m17315\u001b[39m },\n    { seller: \u001b[32m'tdsc'\u001b[39m, product_count: \u001b[33m16583\u001b[39m },\n    { seller: \u001b[32m'frontier'\u001b[39m, product_count: \u001b[33m11260\u001b[39m }\n  ]\n}\n"}, {"text": "Filter functionality test completed successfully\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-31T18:50:53.215Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "b40160f6fba3e7a72973-3caf55594754aede4273", "file": "search-functionality.spec.ts", "line": 104, "column": 3}, {"title": "should test complete search workflow", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "failed", "duration": 10243, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n\n    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:149:31", "location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 149}, "snippet": "\u001b[0m \u001b[90m 147 |\u001b[39m     \u001b[90m// Find and interact with search input\u001b[39m\n \u001b[90m 148 |\u001b[39m     \u001b[36mconst\u001b[39m searchInput \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder*=\"Search\"]'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 149 |\u001b[39m     \u001b[36mawait\u001b[39m expect(searchInput)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 150 |\u001b[39m     \n \u001b[90m 151 |\u001b[39m     \u001b[90m// Type search query\u001b[39m\n \u001b[90m 152 |\u001b[39m     \u001b[36mawait\u001b[39m searchInput\u001b[33m.\u001b[39mfill(\u001b[32m'dental implant'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 149}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n\n\n\u001b[0m \u001b[90m 147 |\u001b[39m     \u001b[90m// Find and interact with search input\u001b[39m\n \u001b[90m 148 |\u001b[39m     \u001b[36mconst\u001b[39m searchInput \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder*=\"Search\"]'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 149 |\u001b[39m     \u001b[36mawait\u001b[39m expect(searchInput)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 150 |\u001b[39m     \n \u001b[90m 151 |\u001b[39m     \u001b[90m// Type search query\u001b[39m\n \u001b[90m 152 |\u001b[39m     \u001b[36mawait\u001b[39m searchInput\u001b[33m.\u001b[39mfill(\u001b[32m'dental implant'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:149:31\u001b[22m"}], "stdout": [{"text": "Testing complete search workflow...\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-31T18:50:53.213Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-515be-st-complete-search-workflow-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-515be-st-complete-search-workflow-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-515be-st-complete-search-workflow-chromium\\error-context.md"}], "errorLocation": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 149}}], "status": "unexpected"}], "id": "b40160f6fba3e7a72973-1a085c36a4f593b2a43d", "file": "search-functionality.spec.ts", "line": 137, "column": 3}, {"title": "should display search suggestions when typing", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "timedOut", "duration": 30455, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m\n    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:4:8", "location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 8, "line": 4}, "snippet": "\u001b[0m \u001b[90m 2 |\u001b[39m\n \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'Search Functionality Tests'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m   |\u001b[39m        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the homepage\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:3000'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 8, "line": 4}, "message": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m\n\n\u001b[0m \u001b[90m 2 |\u001b[39m\n \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'Search Functionality Tests'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m   |\u001b[39m        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the homepage\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:3000'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[0m\n\u001b[2m    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:4:8\u001b[22m"}, {"location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 16, "line": 6}, "message": "Error: page.goto: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the homepage\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:3000'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-31T18:50:53.213Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-78a37-rch-suggestions-when-typing-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-78a37-rch-suggestions-when-typing-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-78a37-rch-suggestions-when-typing-firefox\\error-context.md"}], "errorLocation": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 8, "line": 4}}], "status": "unexpected"}], "id": "b40160f6fba3e7a72973-fd1fb16bf667a027eb19", "file": "search-functionality.spec.ts", "line": 12, "column": 3}, {"title": "should perform search and show results", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 7, "parallelIndex": 2, "status": "timedOut", "duration": 30505, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m\n    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:4:8", "location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 8, "line": 4}, "snippet": "\u001b[0m \u001b[90m 2 |\u001b[39m\n \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'Search Functionality Tests'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m   |\u001b[39m        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the homepage\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:3000'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 8, "line": 4}, "message": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m\n\n\u001b[0m \u001b[90m 2 |\u001b[39m\n \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'Search Functionality Tests'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m   |\u001b[39m        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the homepage\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:3000'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[0m\n\u001b[2m    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:4:8\u001b[22m"}, {"location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 16, "line": 6}, "message": "Error: page.goto: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:3000/\", waiting until \"load\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the homepage\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:3000'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 7 |\u001b[39m     \n \u001b[90m 8 |\u001b[39m     \u001b[90m// Wait for the page to load\u001b[39m\n \u001b[90m 9 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:6:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-31T18:51:10.229Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-68681-orm-search-and-show-results-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-68681-orm-search-and-show-results-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-68681-orm-search-and-show-results-firefox\\error-context.md"}], "errorLocation": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 8, "line": 4}}], "status": "unexpected"}], "id": "b40160f6fba3e7a72973-e52573921a7e196a6cc5", "file": "search-functionality.spec.ts", "line": 40, "column": 3}, {"title": "should test API endpoints directly", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 8, "parallelIndex": 3, "status": "timedOut", "duration": 30040, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m\n    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:4:8", "location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 8, "line": 4}, "snippet": "\u001b[0m \u001b[90m 2 |\u001b[39m\n \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'Search Functionality Tests'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m   |\u001b[39m        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the homepage\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:3000'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 8, "line": 4}, "message": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m\n\n\u001b[0m \u001b[90m 2 |\u001b[39m\n \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'Search Functionality Tests'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m   |\u001b[39m        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the homepage\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:3000'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[0m\n\u001b[2m    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:4:8\u001b[22m"}, {"message": "Error: browserContext.newPage: Test timeout of 30000ms exceeded."}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-31T18:51:11.036Z", "annotations": [], "attachments": [], "errorLocation": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 8, "line": 4}}], "status": "unexpected"}], "id": "b40160f6fba3e7a72973-457582fb246962c6f073", "file": "search-functionality.spec.ts", "line": 70, "column": 3}, {"title": "should test filter functionality", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 6, "parallelIndex": 4, "status": "timedOut", "duration": 30030, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m\n    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:4:8", "location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 8, "line": 4}, "snippet": "\u001b[0m \u001b[90m 2 |\u001b[39m\n \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'Search Functionality Tests'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m   |\u001b[39m        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the homepage\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:3000'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 8, "line": 4}, "message": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m\n\n\u001b[0m \u001b[90m 2 |\u001b[39m\n \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'Search Functionality Tests'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m   |\u001b[39m        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the homepage\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:3000'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[0m\n\u001b[2m    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:4:8\u001b[22m"}, {"message": "Error: browserContext.newPage: Test timeout of 30000ms exceeded."}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-31T18:51:10.120Z", "annotations": [], "attachments": [], "errorLocation": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 8, "line": 4}}], "status": "unexpected"}], "id": "b40160f6fba3e7a72973-7ab1f6fb20b2dbd6d7f3", "file": "search-functionality.spec.ts", "line": 104, "column": 3}, {"title": "should test complete search workflow", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 9, "parallelIndex": 1, "status": "timedOut", "duration": 30040, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m\n    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:4:8", "location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 8, "line": 4}, "snippet": "\u001b[0m \u001b[90m 2 |\u001b[39m\n \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'Search Functionality Tests'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m   |\u001b[39m        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the homepage\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:3000'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[0m"}, "errors": [{"location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 8, "line": 4}, "message": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m\n\n\u001b[0m \u001b[90m 2 |\u001b[39m\n \u001b[90m 3 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'Search Functionality Tests'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 4 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m   |\u001b[39m        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 5 |\u001b[39m     \u001b[90m// Navigate to the homepage\u001b[39m\n \u001b[90m 6 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:3000'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 7 |\u001b[39m     \u001b[0m\n\u001b[2m    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:4:8\u001b[22m"}, {"message": "Error: browserContext.newPage: Test timeout of 30000ms exceeded."}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-07-31T18:51:11.111Z", "annotations": [], "attachments": [], "errorLocation": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 8, "line": 4}}], "status": "unexpected"}], "id": "b40160f6fba3e7a72973-9bfdf99ae2413ac7565a", "file": "search-functionality.spec.ts", "line": 137, "column": 3}, {"title": "should display search suggestions when typing", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 10, "parallelIndex": 0, "status": "failed", "duration": 10895, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n\n    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:17:31", "location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 17}, "snippet": "\u001b[0m \u001b[90m 15 |\u001b[39m     \u001b[90m// Find the search input\u001b[39m\n \u001b[90m 16 |\u001b[39m     \u001b[36mconst\u001b[39m searchInput \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder*=\"Search\"]'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 17 |\u001b[39m     \u001b[36mawait\u001b[39m expect(searchInput)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 18 |\u001b[39m     \n \u001b[90m 19 |\u001b[39m     \u001b[90m// Type in the search input\u001b[39m\n \u001b[90m 20 |\u001b[39m     \u001b[36mawait\u001b[39m searchInput\u001b[33m.\u001b[39mfill(\u001b[32m'dental'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 17}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n\n\n\u001b[0m \u001b[90m 15 |\u001b[39m     \u001b[90m// Find the search input\u001b[39m\n \u001b[90m 16 |\u001b[39m     \u001b[36mconst\u001b[39m searchInput \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder*=\"Search\"]'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 17 |\u001b[39m     \u001b[36mawait\u001b[39m expect(searchInput)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 18 |\u001b[39m     \n \u001b[90m 19 |\u001b[39m     \u001b[90m// Type in the search input\u001b[39m\n \u001b[90m 20 |\u001b[39m     \u001b[36mawait\u001b[39m searchInput\u001b[33m.\u001b[39mfill(\u001b[32m'dental'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:17:31\u001b[22m"}], "stdout": [{"text": "Testing search suggestions...\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-31T18:51:11.279Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-78a37-rch-suggestions-when-typing-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-78a37-rch-suggestions-when-typing-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-78a37-rch-suggestions-when-typing-webkit\\error-context.md"}], "errorLocation": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 17}}], "status": "unexpected"}], "id": "b40160f6fba3e7a72973-18fac8e91f3e2585d3b9", "file": "search-functionality.spec.ts", "line": 12, "column": 3}, {"title": "should perform search and show results", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 11, "parallelIndex": 0, "status": "failed", "duration": 7690, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n\n    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:45:31", "location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 45}, "snippet": "\u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[90m// Find the search input\u001b[39m\n \u001b[90m 44 |\u001b[39m     \u001b[36mconst\u001b[39m searchInput \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder*=\"Search\"]'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 45 |\u001b[39m     \u001b[36mawait\u001b[39m expect(searchInput)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 46 |\u001b[39m     \n \u001b[90m 47 |\u001b[39m     \u001b[90m// Type and press Enter\u001b[39m\n \u001b[90m 48 |\u001b[39m     \u001b[36mawait\u001b[39m searchInput\u001b[33m.\u001b[39mfill(\u001b[32m'dental'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 45}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n\n\n\u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[90m// Find the search input\u001b[39m\n \u001b[90m 44 |\u001b[39m     \u001b[36mconst\u001b[39m searchInput \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder*=\"Search\"]'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 45 |\u001b[39m     \u001b[36mawait\u001b[39m expect(searchInput)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 46 |\u001b[39m     \n \u001b[90m 47 |\u001b[39m     \u001b[90m// Type and press Enter\u001b[39m\n \u001b[90m 48 |\u001b[39m     \u001b[36mawait\u001b[39m searchInput\u001b[33m.\u001b[39mfill(\u001b[32m'dental'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:45:31\u001b[22m"}], "stdout": [{"text": "Testing search results...\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-31T18:51:26.560Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-68681-orm-search-and-show-results-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-68681-orm-search-and-show-results-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-68681-orm-search-and-show-results-webkit\\error-context.md"}], "errorLocation": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 45}}], "status": "unexpected"}], "id": "b40160f6fba3e7a72973-0a519a9a7fab0f08a764", "file": "search-functionality.spec.ts", "line": 40, "column": 3}, {"title": "should test API endpoints directly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 12, "parallelIndex": 5, "status": "passed", "duration": 3609, "errors": [], "stdout": [{"text": "Testing API endpoints...\n"}, {"text": "Suggestions API response: {\n  success: \u001b[33mtrue\u001b[39m,\n  message: \u001b[32m'Suggestions retrieved successfully'\u001b[39m,\n  query: \u001b[32m'dental'\u001b[39m,\n  suggestions: [\n    {\n      id: \u001b[33m26974\u001b[39m,\n      mfr: \u001b[32m'77023-1'\u001b[39m,\n      name: \u001b[32m'Seal-Tight® Auto-Lock Adapter Type \"D\" Dental-Ez'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/3143-117/seal-tight-auto-lock-adapter-type-d-dental-ez'\u001b[39m,\n      maincat: \u001b[32m'Disposables'\u001b[39m,\n      brand: \u001b[32m'Kerr TotalCare'\u001b[39m,\n      manufactured_by: \u001b[32m'kavo-kerr'\u001b[39m,\n      category: \u001b[32m'Disposables & Infection Control'\u001b[39m\n    },\n    {\n      id: \u001b[33m119535\u001b[39m,\n      mfr: \u001b[32m'31009-2'\u001b[39m,\n      name: \u001b[32m'Chairmount Dental-Eze NuSimplicity'\u001b[39m,\n      seller: \u001b[32m'dds'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://ddsdentalsupplies.com/chairmount-dental-eze-nusimplicity/'\u001b[39m,\n      maincat: \u001b[32m'Miscellaneous'\u001b[39m,\n      brand: \u001b[32m'Crosstex International'\u001b[39m,\n      manufactured_by: \u001b[32m'crosstex'\u001b[39m,\n      category: \u001b[32m'Miscellaneous'\u001b[39m\n    },\n    {\n      id: \u001b[33m119536\u001b[39m,\n      mfr: \u001b[32m'31009-1'\u001b[39m,\n      name: \u001b[32m'Chairmount Dental-Eze Simplicity'\u001b[39m,\n      seller: \u001b[32m'dds'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://ddsdentalsupplies.com/chairmount-dental-eze-simplicity/'\u001b[39m,\n      maincat: \u001b[32m'Miscellaneous'\u001b[39m,\n      brand: \u001b[32m'Crosstex International'\u001b[39m,\n      manufactured_by: \u001b[32m'crosstex'\u001b[39m,\n      category: \u001b[32m'Miscellaneous'\u001b[39m\n    },\n    {\n      id: \u001b[33m262197\u001b[39m,\n      mfr: \u001b[32m'FGSS557'\u001b[39m,\n      name: \u001b[32m'DentalRee Dental Ree FG #557 SS (Short Shank) Straight Fissure Crosscut Carbide Bur, 10/Pk'\u001b[39m,\n      seller: \u001b[32m'net32'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://www.net32.com/ec/dentalree-dental-ree-fg-557-ss-short-d-181969?queryID=bb4766df2db464254b5a30d4dc456052'\u001b[39m,\n      maincat: \u001b[32m'Burs & diamonds'\u001b[39m,\n      brand: \u001b[32m'DentalRee'\u001b[39m,\n      manufactured_by: \u001b[32m'dentalree'\u001b[39m,\n      category: \u001b[32m'Rotary Instruments (Burs, Diamonds, Polishers)'\u001b[39m\n    },\n    {\n      id: \u001b[33m263274\u001b[39m,\n      mfr: \u001b[32m'FGSS57'\u001b[39m,\n      name: \u001b[32m'DentalRee Dental Ree FG #57 SS (Short Shank) Straight Fissure Carbide Bur, 10/Pk'\u001b[39m,\n      seller: \u001b[32m'net32'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://www.net32.com/ec/dentalree-dental-ree-fg-57-ss-short-d-181970?queryID=bd26415c286636703f53fa6eec443455'\u001b[39m,\n      maincat: \u001b[32m'Burs & diamonds'\u001b[39m,\n      brand: \u001b[32m'DentalRee'\u001b[39m,\n      manufactured_by: \u001b[32m'dentalree'\u001b[39m,\n      category: \u001b[32m'Rotary Instruments (Burs, Diamonds, Polishers)'\u001b[39m\n    },\n    {\n      id: \u001b[33m283631\u001b[39m,\n      mfr: \u001b[32m'BB030'\u001b[39m,\n      name: \u001b[32m'Block-4-Bite Disposable X-Ray Bite Blocks, Fits Phillips, Dental-Ez Panolite'\u001b[39m,\n      seller: \u001b[32m'net32'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://www.net32.com/ec/block4bite-disposable-xray-bite-blocks-fits-phillips-d-54856?queryID=56125ad20d6ec0db166024722df5de5c'\u001b[39m,\n      maincat: \u001b[32m'X-Ray products'\u001b[39m,\n      brand: \u001b[32m'Plasdent'\u001b[39m,\n      manufactured_by: \u001b[32m'plasdent'\u001b[39m,\n      category: \u001b[32m'Diagnostic Tools & Imaging'\u001b[39m\n    },\n    {\n      id: \u001b[33m332360\u001b[39m,\n      mfr: \u001b[32m'261547'\u001b[39m,\n      name: \u001b[32m'Dental-EZ Handpiece Swivels, 4-Line'\u001b[39m,\n      seller: \u001b[32m'tdsc'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://www.tdsc.com/All-Categories/Dental-Supplies/Handpieces/Handpiece-Tools-%26-Maintenance/Handpiece-Swivels/Dental-EZ-Handpiece-Swivels/p/220396-1?q=&productListName=Category%20Page&dimension8=&pageRef=CATEGORY'\u001b[39m,\n      maincat: \u001b[32m'Handpieces'\u001b[39m,\n      brand: \u001b[32m'STARDENTAL-DENTAL EZ'\u001b[39m,\n      manufactured_by: \u001b[32m'dentalez'\u001b[39m,\n      category: \u001b[32m'Rotary Instruments (Burs, Diamonds, Polishers)'\u001b[39m\n    },\n    {\n      id: \u001b[33m22003\u001b[39m,\n      mfr: \u001b[32m'5133-026'\u001b[39m,\n      name: \u001b[32m'Benco Dental™ Dental Disperse Tip 19GA Yellow Pack of 20'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/5133-026/benco-dental-dental-disperse-tip-19ga-yellow-pack-'\u001b[39m,\n      maincat: \u001b[32m'Cosmetic Dentistry'\u001b[39m,\n      brand: \u001b[32m'BENCO'\u001b[39m,\n      manufactured_by: \u001b[32m'benco'\u001b[39m,\n      category: \u001b[32m'Restorative Materials (Composites, Amalgams, Glass Ionomers)'\u001b[39m\n    },\n    {\n      id: \u001b[33m22004\u001b[39m,\n      mfr: \u001b[32m'5133-035'\u001b[39m,\n      name: \u001b[32m'Benco Dental™ Dental Disperse Tip 19GA Yellow Pack of 100'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/5133-035/benco-dental-dental-disperse-tip-19ga-yellow-pack-'\u001b[39m,\n      maincat: \u001b[32m'Cosmetic Dentistry'\u001b[39m,\n      brand: \u001b[32m'BENCO'\u001b[39m,\n      manufactured_by: \u001b[32m'benco'\u001b[39m,\n      category: \u001b[32m'Restorative Materials (Composites, Amalgams, Glass Ionomers)'\u001b[39m\n    },\n    {\n      id: \u001b[33m25910\u001b[39m,\n      mfr: \u001b[32m'3229-927'\u001b[39m,\n      name: \u001b[32m'Benco Dental™ Blue Thin Rubber Latex Dental Dam 5\"x5\" Box of 52'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/3229-927/benco-dental-blue-thin-rubber-latex-dental-dam-5x5'\u001b[39m,\n      maincat: \u001b[32m'Dental Dam'\u001b[39m,\n      brand: \u001b[32m'BENCO'\u001b[39m,\n      manufactured_by: \u001b[32m'benco'\u001b[39m,\n      category: \u001b[32m'Disposables & Infection Control'\u001b[39m\n    }\n  ]\n}\n"}, {"text": "Search API response: {\n  success: \u001b[33mtrue\u001b[39m,\n  message: \u001b[32m'Search completed successfully (cached)'\u001b[39m,\n  query: \u001b[32m'dental'\u001b[39m,\n  search_type: \u001b[32m'fulltext'\u001b[39m,\n  total: \u001b[33m5810\u001b[39m,\n  results: [\n    {\n      id: \u001b[33m26974\u001b[39m,\n      mfr: \u001b[32m'77023-1'\u001b[39m,\n      name: \u001b[32m'Seal-Tight® Auto-Lock Adapter Type \"D\" Dental-Ez'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/3143-117/seal-tight-auto-lock-adapter-type-d-dental-ez'\u001b[39m,\n      maincat: \u001b[32m'Disposables'\u001b[39m,\n      brand: \u001b[32m'Kerr TotalCare'\u001b[39m,\n      manufactured_by: \u001b[32m'kavo-kerr'\u001b[39m,\n      category: \u001b[32m'Disposables & Infection Control'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'77023-1 | Seal-Tight® Auto-Lock Adapter Type \"D\" Dental-Ez'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m119535\u001b[39m,\n      mfr: \u001b[32m'31009-2'\u001b[39m,\n      name: \u001b[32m'Chairmount Dental-Eze NuSimplicity'\u001b[39m,\n      url: \u001b[32m'https://ddsdentalsupplies.com/chairmount-dental-eze-nusimplicity/'\u001b[39m,\n      maincat: \u001b[32m'Miscellaneous'\u001b[39m,\n      brand: \u001b[32m'Crosstex International'\u001b[39m,\n      manufactured_by: \u001b[32m'crosstex'\u001b[39m,\n      category: \u001b[32m'Miscellaneous'\u001b[39m,\n      seller: \u001b[32m'dds'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'31009-2 | Chairmount Dental-Eze NuSimplicity'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m119536\u001b[39m,\n      mfr: \u001b[32m'31009-1'\u001b[39m,\n      name: \u001b[32m'Chairmount Dental-Eze Simplicity'\u001b[39m,\n      url: \u001b[32m'https://ddsdentalsupplies.com/chairmount-dental-eze-simplicity/'\u001b[39m,\n      maincat: \u001b[32m'Miscellaneous'\u001b[39m,\n      brand: \u001b[32m'Crosstex International'\u001b[39m,\n      manufactured_by: \u001b[32m'crosstex'\u001b[39m,\n      category: \u001b[32m'Miscellaneous'\u001b[39m,\n      seller: \u001b[32m'dds'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'31009-1 | Chairmount Dental-Eze Simplicity'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m262197\u001b[39m,\n      mfr: \u001b[32m'FGSS557'\u001b[39m,\n      name: \u001b[32m'DentalRee Dental Ree FG #557 SS (Short Shank) Straight Fissure Crosscut Carbide Bur, 10/Pk'\u001b[39m,\n      url: \u001b[32m'https://www.net32.com/ec/dentalree-dental-ree-fg-557-ss-short-d-181969?queryID=bb4766df2db464254b5a30d4dc456052'\u001b[39m,\n      maincat: \u001b[32m'Burs & diamonds'\u001b[39m,\n      brand: \u001b[32m'DentalRee'\u001b[39m,\n      manufactured_by: \u001b[32m'dentalree'\u001b[39m,\n      category: \u001b[32m'Rotary Instruments (Burs, Diamonds, Polishers)'\u001b[39m,\n      seller: \u001b[32m'net32'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'FGSS557 | DentalRee Dental Ree FG #557 SS (Short Shank) Straight Fissure Crosscut Carbide Bur, 10/Pk'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m263274\u001b[39m,\n      mfr: \u001b[32m'FGSS57'\u001b[39m,\n      name: \u001b[32m'DentalRee Dental Ree FG #57 SS (Short Shank) Straight Fissure Carbide Bur, 10/Pk'\u001b[39m,\n      url: \u001b[32m'https://www.net32.com/ec/dentalree-dental-ree-fg-57-ss-short-d-181970?queryID=bd26415c286636703f53fa6eec443455'\u001b[39m,\n      maincat: \u001b[32m'Burs & diamonds'\u001b[39m,\n      brand: \u001b[32m'DentalRee'\u001b[39m,\n      manufactured_by: \u001b[32m'dentalree'\u001b[39m,\n      category: \u001b[32m'Rotary Instruments (Burs, Diamonds, Polishers)'\u001b[39m,\n      seller: \u001b[32m'net32'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'FGSS57 | DentalRee Dental Ree FG #57 SS (Short Shank) Straight Fissure Carbide Bur, 10/Pk'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m283631\u001b[39m,\n      mfr: \u001b[32m'BB030'\u001b[39m,\n      name: \u001b[32m'Block-4-Bite Disposable X-Ray Bite Blocks, Fits Phillips, Dental-Ez Panolite'\u001b[39m,\n      url: \u001b[32m'https://www.net32.com/ec/block4bite-disposable-xray-bite-blocks-fits-phillips-d-54856?queryID=56125ad20d6ec0db166024722df5de5c'\u001b[39m,\n      maincat: \u001b[32m'X-Ray products'\u001b[39m,\n      brand: \u001b[32m'Plasdent'\u001b[39m,\n      manufactured_by: \u001b[32m'plasdent'\u001b[39m,\n      category: \u001b[32m'Diagnostic Tools & Imaging'\u001b[39m,\n      seller: \u001b[32m'net32'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'BB030 | Block-4-Bite Disposable X-Ray Bite Blocks, Fits Phillips, Dental-Ez Panolite'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m332360\u001b[39m,\n      mfr: \u001b[32m'261547'\u001b[39m,\n      name: \u001b[32m'Dental-EZ Handpiece Swivels, 4-Line'\u001b[39m,\n      url: \u001b[32m'https://www.tdsc.com/All-Categories/Dental-Supplies/Handpieces/Handpiece-Tools-%26-Maintenance/Handpiece-Swivels/Dental-EZ-Handpiece-Swivels/p/220396-1?q=&productListName=Category%20Page&dimension8=&pageRef=CATEGORY'\u001b[39m,\n      maincat: \u001b[32m'Handpieces'\u001b[39m,\n      brand: \u001b[32m'STARDENTAL-DENTAL EZ'\u001b[39m,\n      manufactured_by: \u001b[32m'dentalez'\u001b[39m,\n      category: \u001b[32m'Rotary Instruments (Burs, Diamonds, Polishers)'\u001b[39m,\n      seller: \u001b[32m'tdsc'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'261547 | Dental-EZ Handpiece Swivels, 4-Line'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m22003\u001b[39m,\n      mfr: \u001b[32m'5133-026'\u001b[39m,\n      name: \u001b[32m'Benco Dental™ Dental Disperse Tip 19GA Yellow Pack of 20'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/5133-026/benco-dental-dental-disperse-tip-19ga-yellow-pack-'\u001b[39m,\n      maincat: \u001b[32m'Cosmetic Dentistry'\u001b[39m,\n      brand: \u001b[32m'BENCO'\u001b[39m,\n      manufactured_by: \u001b[32m'benco'\u001b[39m,\n      category: \u001b[32m'Restorative Materials (Composites, Amalgams, Glass Ionomers)'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'5133-026 | Benco Dental™ Dental Disperse Tip 19GA Yellow Pack of 20'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m22004\u001b[39m,\n      mfr: \u001b[32m'5133-035'\u001b[39m,\n      name: \u001b[32m'Benco Dental™ Dental Disperse Tip 19GA Yellow Pack of 100'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/5133-035/benco-dental-dental-disperse-tip-19ga-yellow-pack-'\u001b[39m,\n      maincat: \u001b[32m'Cosmetic Dentistry'\u001b[39m,\n      brand: \u001b[32m'BENCO'\u001b[39m,\n      manufactured_by: \u001b[32m'benco'\u001b[39m,\n      category: \u001b[32m'Restorative Materials (Composites, Amalgams, Glass Ionomers)'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'5133-035 | Benco Dental™ Dental Disperse Tip 19GA Yellow Pack of 100'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m25910\u001b[39m,\n      mfr: \u001b[32m'3229-927'\u001b[39m,\n      name: \u001b[32m'Benco Dental™ Blue Thin Rubber Latex Dental Dam 5\"x5\" Box of 52'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/3229-927/benco-dental-blue-thin-rubber-latex-dental-dam-5x5'\u001b[39m,\n      maincat: \u001b[32m'Dental Dam'\u001b[39m,\n      brand: \u001b[32m'BENCO'\u001b[39m,\n      manufactured_by: \u001b[32m'benco'\u001b[39m,\n      category: \u001b[32m'Disposables & Infection Control'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'3229-927 | Benco Dental™ Blue Thin Rubber Latex Dental Dam 5\"x5\" Box of 52'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    }\n  ],\n  pagination: {\n    total: \u001b[33m5810\u001b[39m,\n    page: \u001b[33m1\u001b[39m,\n    per_page: \u001b[33m10\u001b[39m,\n    total_pages: \u001b[33m581\u001b[39m,\n    has_next: \u001b[33mtrue\u001b[39m,\n    has_prev: \u001b[33mfalse\u001b[39m\n  },\n  filters_applied: \u001b[1mnull\u001b[22m,\n  search_time_ms: \u001b[33m1.8756389617919922\u001b[39m\n}\n"}, {"text": "Popular searches API response: {\n  success: \u001b[33mtrue\u001b[39m,\n  message: \u001b[32m'Popular searches retrieved successfully'\u001b[39m,\n  popular_searches: [\n    { term: \u001b[32m'Instruments'\u001b[39m, count: \u001b[33m36356\u001b[39m },\n    { term: \u001b[32m'Endodontics'\u001b[39m, count: \u001b[33m21769\u001b[39m },\n    { term: \u001b[32m'Burs & Diamonds'\u001b[39m, count: \u001b[33m19053\u001b[39m },\n    { term: \u001b[32m'Infection Control'\u001b[39m, count: \u001b[33m15785\u001b[39m },\n    { term: \u001b[32m'Hu-<PERSON><PERSON><PERSON>'\u001b[39m, count: \u001b[33m14752\u001b[39m }\n  ]\n}\n"}, {"text": "API endpoints test completed successfully\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-31T18:51:34.833Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "b40160f6fba3e7a72973-b25cb95c59d16f60aec3", "file": "search-functionality.spec.ts", "line": 70, "column": 3}, {"title": "should test filter functionality", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 13, "parallelIndex": 0, "status": "passed", "duration": 3263, "errors": [], "stdout": [{"text": "Testing filter functionality...\n"}, {"text": "Categories API response: {\n  success: \u001b[33mtrue\u001b[39m,\n  message: \u001b[32m'Categories retrieved successfully'\u001b[39m,\n  categories: [\n    {\n      category: \u001b[32m'Rotary Instruments (Burs, Diamonds, Polishers)'\u001b[39m,\n      product_count: \u001b[33m49726\u001b[39m\n    },\n    { category: \u001b[32m'Instruments (Hand)'\u001b[39m, product_count: \u001b[33m42762\u001b[39m },\n    {\n      category: \u001b[32m'Disposables & Infection Control'\u001b[39m,\n      product_count: \u001b[33m36221\u001b[39m\n    },\n    {\n      category: \u001b[32m'Restorative Materials (Composites, Amalgams, Glass Ionomers)'\u001b[39m,\n      product_count: \u001b[33m34559\u001b[39m\n    },\n    { category: \u001b[32m'Endodontics'\u001b[39m, product_count: \u001b[33m26829\u001b[39m },\n    { category: \u001b[32m'Laboratory Products'\u001b[39m, product_count: \u001b[33m24842\u001b[39m },\n    { category: \u001b[32m'Equipment (General)'\u001b[39m, product_count: \u001b[33m17145\u001b[39m },\n    { category: \u001b[32m'Orthodontics'\u001b[39m, product_count: \u001b[33m14315\u001b[39m },\n    { category: \u001b[32m'Preventative & Oral Hygiene'\u001b[39m, product_count: \u001b[33m14299\u001b[39m },\n    {\n      category: \u001b[32m'Prosthodontics (Crowns, Bridges, Dentures)'\u001b[39m,\n      product_count: \u001b[33m13344\u001b[39m\n    },\n    { category: \u001b[32m'Miscellaneous'\u001b[39m, product_count: \u001b[33m13308\u001b[39m },\n    { category: \u001b[32m'Surgical Supplies'\u001b[39m, product_count: \u001b[33m13292\u001b[39m },\n    { category: \u001b[32m'Impression Materials'\u001b[39m, product_count: \u001b[33m11897\u001b[39m },\n    { category: \u001b[32m'Finishing & Polishing'\u001b[39m, product_count: \u001b[33m9048\u001b[39m },\n    { category: \u001b[32m'Diagnostic Tools & Imaging'\u001b[39m, product_count: \u001b[33m6099\u001b[39m },\n    { category: \u001b[32m'Anesthetics & Pain Management'\u001b[39m, product_count: \u001b[33m6041\u001b[39m },\n    { category: \u001b[32m'Cements & Liners'\u001b[39m, product_count: \u001b[33m3207\u001b[39m },\n    { category: \u001b[32m'Bonding & Adhesives'\u001b[39m, product_count: \u001b[33m1217\u001b[39m },\n    { category: \u001b[32m'Waxes & Modeling Materials'\u001b[39m, product_count: \u001b[33m761\u001b[39m },\n    { category: \u001b[32m'Curing Lights'\u001b[39m, product_count: \u001b[33m166\u001b[39m }\n  ]\n}\n"}, {"text": "Manufacturers API response: {\n  success: \u001b[33mtrue\u001b[39m,\n  message: \u001b[32m'Manufacturers retrieved successfully'\u001b[39m,\n  manufacturers: [\n    { manufacturer: \u001b[32m'kavo-kerr'\u001b[39m, product_count: \u001b[33m25380\u001b[39m },\n    { manufacturer: \u001b[32m'hu-friedy'\u001b[39m, product_count: \u001b[33m17549\u001b[39m },\n    { manufacturer: \u001b[32m'dentsply-sirona'\u001b[39m, product_count: \u001b[33m14532\u001b[39m },\n    { manufacturer: \u001b[32m'henry-schein'\u001b[39m, product_count: \u001b[33m11349\u001b[39m },\n    { manufacturer: \u001b[32m'coltene-whaledent'\u001b[39m, product_count: \u001b[33m9412\u001b[39m },\n    { manufacturer: \u001b[32m'gc-america'\u001b[39m, product_count: \u001b[33m7535\u001b[39m },\n    { manufacturer: \u001b[32m'3m-solventum'\u001b[39m, product_count: \u001b[33m7163\u001b[39m },\n    { manufacturer: \u001b[32m'premier-dental'\u001b[39m, product_count: \u001b[33m7108\u001b[39m },\n    { manufacturer: \u001b[32m'kulzer'\u001b[39m, product_count: \u001b[33m6394\u001b[39m },\n    { manufacturer: \u001b[32m'ss-white'\u001b[39m, product_count: \u001b[33m6060\u001b[39m },\n    { manufacturer: \u001b[32m'keystone-industries'\u001b[39m, product_count: \u001b[33m5532\u001b[39m },\n    { manufacturer: \u001b[32m'belmont'\u001b[39m, product_count: \u001b[33m5113\u001b[39m },\n    { manufacturer: \u001b[32m'meisinger'\u001b[39m, product_count: \u001b[33m4947\u001b[39m },\n    { manufacturer: \u001b[32m'ivoclar-vivadent'\u001b[39m, product_count: \u001b[33m4642\u001b[39m },\n    { manufacturer: \u001b[32m'shofu'\u001b[39m, product_count: \u001b[33m4580\u001b[39m },\n    { manufacturer: \u001b[32m'nordent'\u001b[39m, product_count: \u001b[33m4402\u001b[39m },\n    { manufacturer: \u001b[32m'pac-dent'\u001b[39m, product_count: \u001b[33m4164\u001b[39m },\n    { manufacturer: \u001b[32m'zirc'\u001b[39m, product_count: \u001b[33m3856\u001b[39m },\n    { manufacturer: \u001b[32m'integra-miltex'\u001b[39m, product_count: \u001b[33m3802\u001b[39m },\n    { manufacturer: \u001b[32m'benco'\u001b[39m, product_count: \u001b[33m3607\u001b[39m },\n    { manufacturer: \u001b[32m'crosstex'\u001b[39m, product_count: \u001b[33m3346\u001b[39m },\n    { manufacturer: \u001b[32m'darby'\u001b[39m, product_count: \u001b[33m3198\u001b[39m },\n    { manufacturer: \u001b[32m'american-eagle'\u001b[39m, product_count: \u001b[33m2893\u001b[39m },\n    { manufacturer: \u001b[32m'ortho-technology'\u001b[39m, product_count: \u001b[33m2848\u001b[39m },\n    { manufacturer: \u001b[32m'ortho-essentials'\u001b[39m, product_count: \u001b[33m2779\u001b[39m },\n    { manufacturer: \u001b[32m'quala'\u001b[39m, product_count: \u001b[33m2650\u001b[39m },\n    { manufacturer: \u001b[32m'kuraray'\u001b[39m, product_count: \u001b[33m2483\u001b[39m },\n    { manufacturer: \u001b[32m'a-titan'\u001b[39m, product_count: \u001b[33m2455\u001b[39m },\n    { manufacturer: \u001b[32m'ortho-organizers'\u001b[39m, product_count: \u001b[33m2431\u001b[39m },\n    { manufacturer: \u001b[32m'plasdent'\u001b[39m, product_count: \u001b[33m2249\u001b[39m },\n    { manufacturer: \u001b[32m'dentalez'\u001b[39m, product_count: \u001b[33m2236\u001b[39m },\n    { manufacturer: \u001b[32m'young-dental'\u001b[39m, product_count: \u001b[33m2180\u001b[39m },\n    { manufacturer: \u001b[32m'amann-girrbach'\u001b[39m, product_count: \u001b[33m2173\u001b[39m },\n    { manufacturer: \u001b[32m'advance'\u001b[39m, product_count: \u001b[33m2125\u001b[39m },\n    { manufacturer: \u001b[32m'sdi'\u001b[39m, product_count: \u001b[33m2125\u001b[39m },\n    { manufacturer: \u001b[32m'directa'\u001b[39m, product_count: \u001b[33m2070\u001b[39m },\n    { manufacturer: \u001b[32m'palmero'\u001b[39m, product_count: \u001b[33m1994\u001b[39m },\n    { manufacturer: \u001b[32m'medicom'\u001b[39m, product_count: \u001b[33m1953\u001b[39m },\n    { manufacturer: \u001b[32m'diadent'\u001b[39m, product_count: \u001b[33m1858\u001b[39m },\n    { manufacturer: \u001b[32m'voco'\u001b[39m, product_count: \u001b[33m1848\u001b[39m },\n    { manufacturer: \u001b[32m'vista-apex'\u001b[39m, product_count: \u001b[33m1786\u001b[39m },\n    { manufacturer: \u001b[32m'dedeco'\u001b[39m, product_count: \u001b[33m1715\u001b[39m },\n    { manufacturer: \u001b[32m'buffalo-dental'\u001b[39m, product_count: \u001b[33m1711\u001b[39m },\n    { manufacturer: \u001b[32m'safco-dental'\u001b[39m, product_count: \u001b[33m1700\u001b[39m },\n    { manufacturer: \u001b[32m'superior-uniform'\u001b[39m, product_count: \u001b[33m1700\u001b[39m },\n    { manufacturer: \u001b[32m'3b-orthodontics'\u001b[39m, product_count: \u001b[33m1669\u001b[39m },\n    { manufacturer: \u001b[32m'pulpdent'\u001b[39m, product_count: \u001b[33m1644\u001b[39m },\n    { manufacturer: \u001b[32m'microcopy'\u001b[39m, product_count: \u001b[33m1643\u001b[39m },\n    { manufacturer: \u001b[32m'wonderwink'\u001b[39m, product_count: \u001b[33m1583\u001b[39m },\n    { manufacturer: \u001b[32m'j-j-instruments'\u001b[39m, product_count: \u001b[33m1560\u001b[39m },\n    { manufacturer: \u001b[32m'dmg-america'\u001b[39m, product_count: \u001b[33m1543\u001b[39m },\n    { manufacturer: \u001b[32m'edge-endo'\u001b[39m, product_count: \u001b[33m1522\u001b[39m },\n    { manufacturer: \u001b[32m'midmark'\u001b[39m, product_count: \u001b[33m1511\u001b[39m },\n    { manufacturer: \u001b[32m'parkell'\u001b[39m, product_count: \u001b[33m1502\u001b[39m },\n    { manufacturer: \u001b[32m'aidusa'\u001b[39m, product_count: \u001b[33m1463\u001b[39m },\n    { manufacturer: \u001b[32m'denmat'\u001b[39m, product_count: \u001b[33m1456\u001b[39m },\n    { manufacturer: \u001b[32m'ansell'\u001b[39m, product_count: \u001b[33m1424\u001b[39m },\n    { manufacturer: \u001b[32m'valumax'\u001b[39m, product_count: \u001b[33m1368\u001b[39m },\n    { manufacturer: \u001b[32m'garrison-dental'\u001b[39m, product_count: \u001b[33m1349\u001b[39m },\n    { manufacturer: \u001b[32m'vita'\u001b[39m, product_count: \u001b[33m1302\u001b[39m },\n    { manufacturer: \u001b[32m'cargus'\u001b[39m, product_count: \u001b[33m1293\u001b[39m },\n    { manufacturer: \u001b[32m'lang-dental'\u001b[39m, product_count: \u001b[33m1218\u001b[39m },\n    { manufacturer: \u001b[32m'house-brand'\u001b[39m, product_count: \u001b[33m1159\u001b[39m },\n    { manufacturer: \u001b[32m'megagen'\u001b[39m, product_count: \u001b[33m1075\u001b[39m },\n    { manufacturer: \u001b[32m'dukal'\u001b[39m, product_count: \u001b[33m1024\u001b[39m },\n    { manufacturer: \u001b[32m'defend'\u001b[39m, product_count: \u001b[33m1007\u001b[39m },\n    { manufacturer: \u001b[32m'ace-surgical'\u001b[39m, product_count: \u001b[33m989\u001b[39m },\n    { manufacturer: \u001b[32m'danville-materials'\u001b[39m, product_count: \u001b[33m975\u001b[39m },\n    { manufacturer: \u001b[32m'sunstar'\u001b[39m, product_count: \u001b[33m968\u001b[39m },\n    { manufacturer: \u001b[32m'sultan-healthcare'\u001b[39m, product_count: \u001b[33m967\u001b[39m },\n    { manufacturer: \u001b[32m'plak-smacker'\u001b[39m, product_count: \u001b[33m965\u001b[39m },\n    { manufacturer: \u001b[32m'practicon'\u001b[39m, product_count: \u001b[33m960\u001b[39m },\n    { manufacturer: \u001b[32m'centrix'\u001b[39m, product_count: \u001b[33m934\u001b[39m },\n    { manufacturer: \u001b[32m'cardinal-health'\u001b[39m, product_count: \u001b[33m923\u001b[39m },\n    { manufacturer: \u001b[32m'flow-dental'\u001b[39m, product_count: \u001b[33m920\u001b[39m },\n    { manufacturer: \u001b[32m'nsk'\u001b[39m, product_count: \u001b[33m910\u001b[39m },\n    { manufacturer: \u001b[32m'js-dental'\u001b[39m, product_count: \u001b[33m877\u001b[39m },\n    { manufacturer: \u001b[32m'tokuyama'\u001b[39m, product_count: \u001b[33m860\u001b[39m },\n    { manufacturer: \u001b[32m'tidi-products'\u001b[39m, product_count: \u001b[33m854\u001b[39m },\n    { manufacturer: \u001b[32m'roydent'\u001b[39m, product_count: \u001b[33m853\u001b[39m },\n    { manufacturer: \u001b[32m'hager-worldwide'\u001b[39m, product_count: \u001b[33m849\u001b[39m },\n    { manufacturer: \u001b[32m'acteon'\u001b[39m, product_count: \u001b[33m838\u001b[39m },\n    { manufacturer: \u001b[32m'halyard'\u001b[39m, product_count: \u001b[33m831\u001b[39m },\n    { manufacturer: \u001b[32m'clinicians-choice'\u001b[39m, product_count: \u001b[33m827\u001b[39m },\n    { manufacturer: \u001b[32m'smistr'\u001b[39m, product_count: \u001b[33m821\u001b[39m },\n    { manufacturer: \u001b[32m'parker-hannifin'\u001b[39m, product_count: \u001b[33m798\u001b[39m },\n    { manufacturer: \u001b[32m'procter-gamble'\u001b[39m, product_count: \u001b[33m798\u001b[39m },\n    { manufacturer: \u001b[32m'ods'\u001b[39m, product_count: \u001b[33m786\u001b[39m },\n    { manufacturer: \u001b[32m'brasseler'\u001b[39m, product_count: \u001b[33m774\u001b[39m },\n    { manufacturer: \u001b[32m'accutron'\u001b[39m, product_count: \u001b[33m767\u001b[39m },\n    { manufacturer: \u001b[32m'waterpik'\u001b[39m, product_count: \u001b[33m760\u001b[39m },\n    { manufacturer: \u001b[32m'pdt'\u001b[39m, product_count: \u001b[33m734\u001b[39m },\n    { manufacturer: \u001b[32m'paradise-dental'\u001b[39m, product_count: \u001b[33m727\u001b[39m },\n    { manufacturer: \u001b[32m'air-techniques'\u001b[39m, product_count: \u001b[33m706\u001b[39m },\n    { manufacturer: \u001b[32m'dci-international'\u001b[39m, product_count: \u001b[33m701\u001b[39m },\n    { manufacturer: \u001b[32m'spring-health'\u001b[39m, product_count: \u001b[33m700\u001b[39m },\n    { manufacturer: \u001b[32m'sagemax'\u001b[39m, product_count: \u001b[33m684\u001b[39m },\n    { manufacturer: \u001b[32m'essential-dental'\u001b[39m, product_count: \u001b[33m672\u001b[39m },\n    { manufacturer: \u001b[32m'ismile-dental'\u001b[39m, product_count: \u001b[33m671\u001b[39m },\n    { manufacturer: \u001b[32m'acero-crowns'\u001b[39m, product_count: \u001b[33m658\u001b[39m }\n  ]\n}\n"}, {"text": "Sellers API response: {\n  success: \u001b[33mtrue\u001b[39m,\n  message: \u001b[32m'Sellers retrieved successfully'\u001b[39m,\n  sellers: [\n    { seller: \u001b[32m'benco'\u001b[39m, product_count: \u001b[33m82084\u001b[39m },\n    { seller: \u001b[32m'henry'\u001b[39m, product_count: \u001b[33m76867\u001b[39m },\n    { seller: \u001b[32m'midwest'\u001b[39m, product_count: \u001b[33m41946\u001b[39m },\n    { seller: \u001b[32m'darby'\u001b[39m, product_count: \u001b[33m27778\u001b[39m },\n    { seller: \u001b[32m'net32'\u001b[39m, product_count: \u001b[33m26862\u001b[39m },\n    { seller: \u001b[32m'optimus'\u001b[39m, product_count: \u001b[33m20714\u001b[39m },\n    { seller: \u001b[32m'safco'\u001b[39m, product_count: \u001b[33m17669\u001b[39m },\n    { seller: \u001b[32m'dds'\u001b[39m, product_count: \u001b[33m17315\u001b[39m },\n    { seller: \u001b[32m'tdsc'\u001b[39m, product_count: \u001b[33m16583\u001b[39m },\n    { seller: \u001b[32m'frontier'\u001b[39m, product_count: \u001b[33m11260\u001b[39m }\n  ]\n}\n"}, {"text": "Filter functionality test completed successfully\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-31T18:51:37.094Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "b40160f6fba3e7a72973-3ed33e7505cceaa8c691", "file": "search-functionality.spec.ts", "line": 104, "column": 3}, {"title": "should test complete search workflow", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 12, "parallelIndex": 5, "status": "failed", "duration": 8950, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n\n    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:149:31", "location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 149}, "snippet": "\u001b[0m \u001b[90m 147 |\u001b[39m     \u001b[90m// Find and interact with search input\u001b[39m\n \u001b[90m 148 |\u001b[39m     \u001b[36mconst\u001b[39m searchInput \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder*=\"Search\"]'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 149 |\u001b[39m     \u001b[36mawait\u001b[39m expect(searchInput)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 150 |\u001b[39m     \n \u001b[90m 151 |\u001b[39m     \u001b[90m// Type search query\u001b[39m\n \u001b[90m 152 |\u001b[39m     \u001b[36mawait\u001b[39m searchInput\u001b[33m.\u001b[39mfill(\u001b[32m'dental implant'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 149}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n\n\n\u001b[0m \u001b[90m 147 |\u001b[39m     \u001b[90m// Find and interact with search input\u001b[39m\n \u001b[90m 148 |\u001b[39m     \u001b[36mconst\u001b[39m searchInput \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder*=\"Search\"]'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 149 |\u001b[39m     \u001b[36mawait\u001b[39m expect(searchInput)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 150 |\u001b[39m     \n \u001b[90m 151 |\u001b[39m     \u001b[90m// Type search query\u001b[39m\n \u001b[90m 152 |\u001b[39m     \u001b[36mawait\u001b[39m searchInput\u001b[33m.\u001b[39mfill(\u001b[32m'dental implant'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:149:31\u001b[22m"}], "stdout": [{"text": "Testing complete search workflow...\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-31T18:51:38.603Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-515be-st-complete-search-workflow-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-515be-st-complete-search-workflow-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-515be-st-complete-search-workflow-webkit\\error-context.md"}], "errorLocation": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 149}}], "status": "unexpected"}], "id": "b40160f6fba3e7a72973-500b26f278dc0006109d", "file": "search-functionality.spec.ts", "line": 137, "column": 3}, {"title": "should display search suggestions when typing", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 14, "parallelIndex": 0, "status": "failed", "duration": 7465, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n\n    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:17:31", "location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 17}, "snippet": "\u001b[0m \u001b[90m 15 |\u001b[39m     \u001b[90m// Find the search input\u001b[39m\n \u001b[90m 16 |\u001b[39m     \u001b[36mconst\u001b[39m searchInput \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder*=\"Search\"]'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 17 |\u001b[39m     \u001b[36mawait\u001b[39m expect(searchInput)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 18 |\u001b[39m     \n \u001b[90m 19 |\u001b[39m     \u001b[90m// Type in the search input\u001b[39m\n \u001b[90m 20 |\u001b[39m     \u001b[36mawait\u001b[39m searchInput\u001b[33m.\u001b[39mfill(\u001b[32m'dental'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 17}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n\n\n\u001b[0m \u001b[90m 15 |\u001b[39m     \u001b[90m// Find the search input\u001b[39m\n \u001b[90m 16 |\u001b[39m     \u001b[36mconst\u001b[39m searchInput \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder*=\"Search\"]'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 17 |\u001b[39m     \u001b[36mawait\u001b[39m expect(searchInput)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 18 |\u001b[39m     \n \u001b[90m 19 |\u001b[39m     \u001b[90m// Type in the search input\u001b[39m\n \u001b[90m 20 |\u001b[39m     \u001b[36mawait\u001b[39m searchInput\u001b[33m.\u001b[39mfill(\u001b[32m'dental'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:17:31\u001b[22m"}], "stdout": [{"text": "Testing search suggestions...\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-31T18:51:42.244Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-78a37-rch-suggestions-when-typing-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-78a37-rch-suggestions-when-typing-Mobile-Chrome\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-78a37-rch-suggestions-when-typing-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 17}}], "status": "unexpected"}], "id": "b40160f6fba3e7a72973-62510daa3546c44af4ea", "file": "search-functionality.spec.ts", "line": 12, "column": 3}, {"title": "should perform search and show results", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 15, "parallelIndex": 4, "status": "failed", "duration": 7948, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n\n    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:45:31", "location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 45}, "snippet": "\u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[90m// Find the search input\u001b[39m\n \u001b[90m 44 |\u001b[39m     \u001b[36mconst\u001b[39m searchInput \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder*=\"Search\"]'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 45 |\u001b[39m     \u001b[36mawait\u001b[39m expect(searchInput)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 46 |\u001b[39m     \n \u001b[90m 47 |\u001b[39m     \u001b[90m// Type and press Enter\u001b[39m\n \u001b[90m 48 |\u001b[39m     \u001b[36mawait\u001b[39m searchInput\u001b[33m.\u001b[39mfill(\u001b[32m'dental'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 45}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n\n\n\u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[90m// Find the search input\u001b[39m\n \u001b[90m 44 |\u001b[39m     \u001b[36mconst\u001b[39m searchInput \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder*=\"Search\"]'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 45 |\u001b[39m     \u001b[36mawait\u001b[39m expect(searchInput)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 46 |\u001b[39m     \n \u001b[90m 47 |\u001b[39m     \u001b[90m// Type and press Enter\u001b[39m\n \u001b[90m 48 |\u001b[39m     \u001b[36mawait\u001b[39m searchInput\u001b[33m.\u001b[39mfill(\u001b[32m'dental'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:45:31\u001b[22m"}], "stdout": [{"text": "Testing search results...\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-31T18:51:44.810Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-68681-orm-search-and-show-results-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-68681-orm-search-and-show-results-Mobile-Chrome\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-68681-orm-search-and-show-results-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 45}}], "status": "unexpected"}], "id": "b40160f6fba3e7a72973-5f9a6ef626b0ad16773d", "file": "search-functionality.spec.ts", "line": 40, "column": 3}, {"title": "should test API endpoints directly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 16, "parallelIndex": 3, "status": "passed", "duration": 2775, "errors": [], "stdout": [{"text": "Testing API endpoints...\n"}, {"text": "Suggestions API response: {\n  success: \u001b[33mtrue\u001b[39m,\n  message: \u001b[32m'Suggestions retrieved successfully'\u001b[39m,\n  query: \u001b[32m'dental'\u001b[39m,\n  suggestions: [\n    {\n      id: \u001b[33m26974\u001b[39m,\n      mfr: \u001b[32m'77023-1'\u001b[39m,\n      name: \u001b[32m'Seal-Tight® Auto-Lock Adapter Type \"D\" Dental-Ez'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/3143-117/seal-tight-auto-lock-adapter-type-d-dental-ez'\u001b[39m,\n      maincat: \u001b[32m'Disposables'\u001b[39m,\n      brand: \u001b[32m'Kerr TotalCare'\u001b[39m,\n      manufactured_by: \u001b[32m'kavo-kerr'\u001b[39m,\n      category: \u001b[32m'Disposables & Infection Control'\u001b[39m\n    },\n    {\n      id: \u001b[33m119535\u001b[39m,\n      mfr: \u001b[32m'31009-2'\u001b[39m,\n      name: \u001b[32m'Chairmount Dental-Eze NuSimplicity'\u001b[39m,\n      seller: \u001b[32m'dds'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://ddsdentalsupplies.com/chairmount-dental-eze-nusimplicity/'\u001b[39m,\n      maincat: \u001b[32m'Miscellaneous'\u001b[39m,\n      brand: \u001b[32m'Crosstex International'\u001b[39m,\n      manufactured_by: \u001b[32m'crosstex'\u001b[39m,\n      category: \u001b[32m'Miscellaneous'\u001b[39m\n    },\n    {\n      id: \u001b[33m119536\u001b[39m,\n      mfr: \u001b[32m'31009-1'\u001b[39m,\n      name: \u001b[32m'Chairmount Dental-Eze Simplicity'\u001b[39m,\n      seller: \u001b[32m'dds'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://ddsdentalsupplies.com/chairmount-dental-eze-simplicity/'\u001b[39m,\n      maincat: \u001b[32m'Miscellaneous'\u001b[39m,\n      brand: \u001b[32m'Crosstex International'\u001b[39m,\n      manufactured_by: \u001b[32m'crosstex'\u001b[39m,\n      category: \u001b[32m'Miscellaneous'\u001b[39m\n    },\n    {\n      id: \u001b[33m262197\u001b[39m,\n      mfr: \u001b[32m'FGSS557'\u001b[39m,\n      name: \u001b[32m'DentalRee Dental Ree FG #557 SS (Short Shank) Straight Fissure Crosscut Carbide Bur, 10/Pk'\u001b[39m,\n      seller: \u001b[32m'net32'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://www.net32.com/ec/dentalree-dental-ree-fg-557-ss-short-d-181969?queryID=bb4766df2db464254b5a30d4dc456052'\u001b[39m,\n      maincat: \u001b[32m'Burs & diamonds'\u001b[39m,\n      brand: \u001b[32m'DentalRee'\u001b[39m,\n      manufactured_by: \u001b[32m'dentalree'\u001b[39m,\n      category: \u001b[32m'Rotary Instruments (Burs, Diamonds, Polishers)'\u001b[39m\n    },\n    {\n      id: \u001b[33m263274\u001b[39m,\n      mfr: \u001b[32m'FGSS57'\u001b[39m,\n      name: \u001b[32m'DentalRee Dental Ree FG #57 SS (Short Shank) Straight Fissure Carbide Bur, 10/Pk'\u001b[39m,\n      seller: \u001b[32m'net32'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://www.net32.com/ec/dentalree-dental-ree-fg-57-ss-short-d-181970?queryID=bd26415c286636703f53fa6eec443455'\u001b[39m,\n      maincat: \u001b[32m'Burs & diamonds'\u001b[39m,\n      brand: \u001b[32m'DentalRee'\u001b[39m,\n      manufactured_by: \u001b[32m'dentalree'\u001b[39m,\n      category: \u001b[32m'Rotary Instruments (Burs, Diamonds, Polishers)'\u001b[39m\n    },\n    {\n      id: \u001b[33m283631\u001b[39m,\n      mfr: \u001b[32m'BB030'\u001b[39m,\n      name: \u001b[32m'Block-4-Bite Disposable X-Ray Bite Blocks, Fits Phillips, Dental-Ez Panolite'\u001b[39m,\n      seller: \u001b[32m'net32'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://www.net32.com/ec/block4bite-disposable-xray-bite-blocks-fits-phillips-d-54856?queryID=56125ad20d6ec0db166024722df5de5c'\u001b[39m,\n      maincat: \u001b[32m'X-Ray products'\u001b[39m,\n      brand: \u001b[32m'Plasdent'\u001b[39m,\n      manufactured_by: \u001b[32m'plasdent'\u001b[39m,\n      category: \u001b[32m'Diagnostic Tools & Imaging'\u001b[39m\n    },\n    {\n      id: \u001b[33m332360\u001b[39m,\n      mfr: \u001b[32m'261547'\u001b[39m,\n      name: \u001b[32m'Dental-EZ Handpiece Swivels, 4-Line'\u001b[39m,\n      seller: \u001b[32m'tdsc'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://www.tdsc.com/All-Categories/Dental-Supplies/Handpieces/Handpiece-Tools-%26-Maintenance/Handpiece-Swivels/Dental-EZ-Handpiece-Swivels/p/220396-1?q=&productListName=Category%20Page&dimension8=&pageRef=CATEGORY'\u001b[39m,\n      maincat: \u001b[32m'Handpieces'\u001b[39m,\n      brand: \u001b[32m'STARDENTAL-DENTAL EZ'\u001b[39m,\n      manufactured_by: \u001b[32m'dentalez'\u001b[39m,\n      category: \u001b[32m'Rotary Instruments (Burs, Diamonds, Polishers)'\u001b[39m\n    },\n    {\n      id: \u001b[33m22003\u001b[39m,\n      mfr: \u001b[32m'5133-026'\u001b[39m,\n      name: \u001b[32m'Benco Dental™ Dental Disperse Tip 19GA Yellow Pack of 20'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/5133-026/benco-dental-dental-disperse-tip-19ga-yellow-pack-'\u001b[39m,\n      maincat: \u001b[32m'Cosmetic Dentistry'\u001b[39m,\n      brand: \u001b[32m'BENCO'\u001b[39m,\n      manufactured_by: \u001b[32m'benco'\u001b[39m,\n      category: \u001b[32m'Restorative Materials (Composites, Amalgams, Glass Ionomers)'\u001b[39m\n    },\n    {\n      id: \u001b[33m22004\u001b[39m,\n      mfr: \u001b[32m'5133-035'\u001b[39m,\n      name: \u001b[32m'Benco Dental™ Dental Disperse Tip 19GA Yellow Pack of 100'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/5133-035/benco-dental-dental-disperse-tip-19ga-yellow-pack-'\u001b[39m,\n      maincat: \u001b[32m'Cosmetic Dentistry'\u001b[39m,\n      brand: \u001b[32m'BENCO'\u001b[39m,\n      manufactured_by: \u001b[32m'benco'\u001b[39m,\n      category: \u001b[32m'Restorative Materials (Composites, Amalgams, Glass Ionomers)'\u001b[39m\n    },\n    {\n      id: \u001b[33m25910\u001b[39m,\n      mfr: \u001b[32m'3229-927'\u001b[39m,\n      name: \u001b[32m'Benco Dental™ Blue Thin Rubber Latex Dental Dam 5\"x5\" Box of 52'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/3229-927/benco-dental-blue-thin-rubber-latex-dental-dam-5x5'\u001b[39m,\n      maincat: \u001b[32m'Dental Dam'\u001b[39m,\n      brand: \u001b[32m'BENCO'\u001b[39m,\n      manufactured_by: \u001b[32m'benco'\u001b[39m,\n      category: \u001b[32m'Disposables & Infection Control'\u001b[39m\n    }\n  ]\n}\n"}, {"text": "Search API response: {\n  success: \u001b[33mtrue\u001b[39m,\n  message: \u001b[32m'Search completed successfully (cached)'\u001b[39m,\n  query: \u001b[32m'dental'\u001b[39m,\n  search_type: \u001b[32m'fulltext'\u001b[39m,\n  total: \u001b[33m5810\u001b[39m,\n  results: [\n    {\n      id: \u001b[33m26974\u001b[39m,\n      mfr: \u001b[32m'77023-1'\u001b[39m,\n      name: \u001b[32m'Seal-Tight® Auto-Lock Adapter Type \"D\" Dental-Ez'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/3143-117/seal-tight-auto-lock-adapter-type-d-dental-ez'\u001b[39m,\n      maincat: \u001b[32m'Disposables'\u001b[39m,\n      brand: \u001b[32m'Kerr TotalCare'\u001b[39m,\n      manufactured_by: \u001b[32m'kavo-kerr'\u001b[39m,\n      category: \u001b[32m'Disposables & Infection Control'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'77023-1 | Seal-Tight® Auto-Lock Adapter Type \"D\" Dental-Ez'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m119535\u001b[39m,\n      mfr: \u001b[32m'31009-2'\u001b[39m,\n      name: \u001b[32m'Chairmount Dental-Eze NuSimplicity'\u001b[39m,\n      url: \u001b[32m'https://ddsdentalsupplies.com/chairmount-dental-eze-nusimplicity/'\u001b[39m,\n      maincat: \u001b[32m'Miscellaneous'\u001b[39m,\n      brand: \u001b[32m'Crosstex International'\u001b[39m,\n      manufactured_by: \u001b[32m'crosstex'\u001b[39m,\n      category: \u001b[32m'Miscellaneous'\u001b[39m,\n      seller: \u001b[32m'dds'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'31009-2 | Chairmount Dental-Eze NuSimplicity'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m119536\u001b[39m,\n      mfr: \u001b[32m'31009-1'\u001b[39m,\n      name: \u001b[32m'Chairmount Dental-Eze Simplicity'\u001b[39m,\n      url: \u001b[32m'https://ddsdentalsupplies.com/chairmount-dental-eze-simplicity/'\u001b[39m,\n      maincat: \u001b[32m'Miscellaneous'\u001b[39m,\n      brand: \u001b[32m'Crosstex International'\u001b[39m,\n      manufactured_by: \u001b[32m'crosstex'\u001b[39m,\n      category: \u001b[32m'Miscellaneous'\u001b[39m,\n      seller: \u001b[32m'dds'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'31009-1 | Chairmount Dental-Eze Simplicity'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m262197\u001b[39m,\n      mfr: \u001b[32m'FGSS557'\u001b[39m,\n      name: \u001b[32m'DentalRee Dental Ree FG #557 SS (Short Shank) Straight Fissure Crosscut Carbide Bur, 10/Pk'\u001b[39m,\n      url: \u001b[32m'https://www.net32.com/ec/dentalree-dental-ree-fg-557-ss-short-d-181969?queryID=bb4766df2db464254b5a30d4dc456052'\u001b[39m,\n      maincat: \u001b[32m'Burs & diamonds'\u001b[39m,\n      brand: \u001b[32m'DentalRee'\u001b[39m,\n      manufactured_by: \u001b[32m'dentalree'\u001b[39m,\n      category: \u001b[32m'Rotary Instruments (Burs, Diamonds, Polishers)'\u001b[39m,\n      seller: \u001b[32m'net32'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'FGSS557 | DentalRee Dental Ree FG #557 SS (Short Shank) Straight Fissure Crosscut Carbide Bur, 10/Pk'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m263274\u001b[39m,\n      mfr: \u001b[32m'FGSS57'\u001b[39m,\n      name: \u001b[32m'DentalRee Dental Ree FG #57 SS (Short Shank) Straight Fissure Carbide Bur, 10/Pk'\u001b[39m,\n      url: \u001b[32m'https://www.net32.com/ec/dentalree-dental-ree-fg-57-ss-short-d-181970?queryID=bd26415c286636703f53fa6eec443455'\u001b[39m,\n      maincat: \u001b[32m'Burs & diamonds'\u001b[39m,\n      brand: \u001b[32m'DentalRee'\u001b[39m,\n      manufactured_by: \u001b[32m'dentalree'\u001b[39m,\n      category: \u001b[32m'Rotary Instruments (Burs, Diamonds, Polishers)'\u001b[39m,\n      seller: \u001b[32m'net32'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'FGSS57 | DentalRee Dental Ree FG #57 SS (Short Shank) Straight Fissure Carbide Bur, 10/Pk'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m283631\u001b[39m,\n      mfr: \u001b[32m'BB030'\u001b[39m,\n      name: \u001b[32m'Block-4-Bite Disposable X-Ray Bite Blocks, Fits Phillips, Dental-Ez Panolite'\u001b[39m,\n      url: \u001b[32m'https://www.net32.com/ec/block4bite-disposable-xray-bite-blocks-fits-phillips-d-54856?queryID=56125ad20d6ec0db166024722df5de5c'\u001b[39m,\n      maincat: \u001b[32m'X-Ray products'\u001b[39m,\n      brand: \u001b[32m'Plasdent'\u001b[39m,\n      manufactured_by: \u001b[32m'plasdent'\u001b[39m,\n      category: \u001b[32m'Diagnostic Tools & Imaging'\u001b[39m,\n      seller: \u001b[32m'net32'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'BB030 | Block-4-Bite Disposable X-Ray Bite Blocks, Fits Phillips, Dental-Ez Panolite'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m332360\u001b[39m,\n      mfr: \u001b[32m'261547'\u001b[39m,\n      name: \u001b[32m'Dental-EZ Handpiece Swivels, 4-Line'\u001b[39m,\n      url: \u001b[32m'https://www.tdsc.com/All-Categories/Dental-Supplies/Handpieces/Handpiece-Tools-%26-Maintenance/Handpiece-Swivels/Dental-EZ-Handpiece-Swivels/p/220396-1?q=&productListName=Category%20Page&dimension8=&pageRef=CATEGORY'\u001b[39m,\n      maincat: \u001b[32m'Handpieces'\u001b[39m,\n      brand: \u001b[32m'STARDENTAL-DENTAL EZ'\u001b[39m,\n      manufactured_by: \u001b[32m'dentalez'\u001b[39m,\n      category: \u001b[32m'Rotary Instruments (Burs, Diamonds, Polishers)'\u001b[39m,\n      seller: \u001b[32m'tdsc'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'261547 | Dental-EZ Handpiece Swivels, 4-Line'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m22003\u001b[39m,\n      mfr: \u001b[32m'5133-026'\u001b[39m,\n      name: \u001b[32m'Benco Dental™ Dental Disperse Tip 19GA Yellow Pack of 20'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/5133-026/benco-dental-dental-disperse-tip-19ga-yellow-pack-'\u001b[39m,\n      maincat: \u001b[32m'Cosmetic Dentistry'\u001b[39m,\n      brand: \u001b[32m'BENCO'\u001b[39m,\n      manufactured_by: \u001b[32m'benco'\u001b[39m,\n      category: \u001b[32m'Restorative Materials (Composites, Amalgams, Glass Ionomers)'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'5133-026 | Benco Dental™ Dental Disperse Tip 19GA Yellow Pack of 20'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m22004\u001b[39m,\n      mfr: \u001b[32m'5133-035'\u001b[39m,\n      name: \u001b[32m'Benco Dental™ Dental Disperse Tip 19GA Yellow Pack of 100'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/5133-035/benco-dental-dental-disperse-tip-19ga-yellow-pack-'\u001b[39m,\n      maincat: \u001b[32m'Cosmetic Dentistry'\u001b[39m,\n      brand: \u001b[32m'BENCO'\u001b[39m,\n      manufactured_by: \u001b[32m'benco'\u001b[39m,\n      category: \u001b[32m'Restorative Materials (Composites, Amalgams, Glass Ionomers)'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'5133-035 | Benco Dental™ Dental Disperse Tip 19GA Yellow Pack of 100'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m25910\u001b[39m,\n      mfr: \u001b[32m'3229-927'\u001b[39m,\n      name: \u001b[32m'Benco Dental™ Blue Thin Rubber Latex Dental Dam 5\"x5\" Box of 52'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/3229-927/benco-dental-blue-thin-rubber-latex-dental-dam-5x5'\u001b[39m,\n      maincat: \u001b[32m'Dental Dam'\u001b[39m,\n      brand: \u001b[32m'BENCO'\u001b[39m,\n      manufactured_by: \u001b[32m'benco'\u001b[39m,\n      category: \u001b[32m'Disposables & Infection Control'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'3229-927 | Benco Dental™ Blue Thin Rubber Latex Dental Dam 5\"x5\" Box of 52'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    }\n  ],\n  pagination: {\n    total: \u001b[33m5810\u001b[39m,\n    page: \u001b[33m1\u001b[39m,\n    per_page: \u001b[33m10\u001b[39m,\n    total_pages: \u001b[33m581\u001b[39m,\n    has_next: \u001b[33mtrue\u001b[39m,\n    has_prev: \u001b[33mfalse\u001b[39m\n  },\n  filters_applied: \u001b[1mnull\u001b[22m,\n  search_time_ms: \u001b[33m2.6094913482666016\u001b[39m\n}\n"}, {"text": "Popular searches API response: {\n  success: \u001b[33mtrue\u001b[39m,\n  message: \u001b[32m'Popular searches retrieved successfully'\u001b[39m,\n  popular_searches: [\n    { term: \u001b[32m'Instruments'\u001b[39m, count: \u001b[33m36356\u001b[39m },\n    { term: \u001b[32m'Endodontics'\u001b[39m, count: \u001b[33m21769\u001b[39m },\n    { term: \u001b[32m'Burs & Diamonds'\u001b[39m, count: \u001b[33m19053\u001b[39m },\n    { term: \u001b[32m'Infection Control'\u001b[39m, count: \u001b[33m15785\u001b[39m },\n    { term: \u001b[32m'Hu-<PERSON><PERSON><PERSON>'\u001b[39m, count: \u001b[33m14752\u001b[39m }\n  ]\n}\n"}, {"text": "API endpoints test completed successfully\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-31T18:51:47.468Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "b40160f6fba3e7a72973-66a70b96f4f5a8e7b2fa", "file": "search-functionality.spec.ts", "line": 70, "column": 3}, {"title": "should test filter functionality", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 17, "parallelIndex": 5, "status": "passed", "duration": 3440, "errors": [], "stdout": [{"text": "Testing filter functionality...\n"}, {"text": "Categories API response: {\n  success: \u001b[33mtrue\u001b[39m,\n  message: \u001b[32m'Categories retrieved successfully'\u001b[39m,\n  categories: [\n    {\n      category: \u001b[32m'Rotary Instruments (Burs, Diamonds, Polishers)'\u001b[39m,\n      product_count: \u001b[33m49726\u001b[39m\n    },\n    { category: \u001b[32m'Instruments (Hand)'\u001b[39m, product_count: \u001b[33m42762\u001b[39m },\n    {\n      category: \u001b[32m'Disposables & Infection Control'\u001b[39m,\n      product_count: \u001b[33m36221\u001b[39m\n    },\n    {\n      category: \u001b[32m'Restorative Materials (Composites, Amalgams, Glass Ionomers)'\u001b[39m,\n      product_count: \u001b[33m34559\u001b[39m\n    },\n    { category: \u001b[32m'Endodontics'\u001b[39m, product_count: \u001b[33m26829\u001b[39m },\n    { category: \u001b[32m'Laboratory Products'\u001b[39m, product_count: \u001b[33m24842\u001b[39m },\n    { category: \u001b[32m'Equipment (General)'\u001b[39m, product_count: \u001b[33m17145\u001b[39m },\n    { category: \u001b[32m'Orthodontics'\u001b[39m, product_count: \u001b[33m14315\u001b[39m },\n    { category: \u001b[32m'Preventative & Oral Hygiene'\u001b[39m, product_count: \u001b[33m14299\u001b[39m },\n    {\n      category: \u001b[32m'Prosthodontics (Crowns, Bridges, Dentures)'\u001b[39m,\n      product_count: \u001b[33m13344\u001b[39m\n    },\n    { category: \u001b[32m'Miscellaneous'\u001b[39m, product_count: \u001b[33m13308\u001b[39m },\n    { category: \u001b[32m'Surgical Supplies'\u001b[39m, product_count: \u001b[33m13292\u001b[39m },\n    { category: \u001b[32m'Impression Materials'\u001b[39m, product_count: \u001b[33m11897\u001b[39m },\n    { category: \u001b[32m'Finishing & Polishing'\u001b[39m, product_count: \u001b[33m9048\u001b[39m },\n    { category: \u001b[32m'Diagnostic Tools & Imaging'\u001b[39m, product_count: \u001b[33m6099\u001b[39m },\n    { category: \u001b[32m'Anesthetics & Pain Management'\u001b[39m, product_count: \u001b[33m6041\u001b[39m },\n    { category: \u001b[32m'Cements & Liners'\u001b[39m, product_count: \u001b[33m3207\u001b[39m },\n    { category: \u001b[32m'Bonding & Adhesives'\u001b[39m, product_count: \u001b[33m1217\u001b[39m },\n    { category: \u001b[32m'Waxes & Modeling Materials'\u001b[39m, product_count: \u001b[33m761\u001b[39m },\n    { category: \u001b[32m'Curing Lights'\u001b[39m, product_count: \u001b[33m166\u001b[39m }\n  ]\n}\n"}, {"text": "Manufacturers API response: {\n  success: \u001b[33mtrue\u001b[39m,\n  message: \u001b[32m'Manufacturers retrieved successfully'\u001b[39m,\n  manufacturers: [\n    { manufacturer: \u001b[32m'kavo-kerr'\u001b[39m, product_count: \u001b[33m25380\u001b[39m },\n    { manufacturer: \u001b[32m'hu-friedy'\u001b[39m, product_count: \u001b[33m17549\u001b[39m },\n    { manufacturer: \u001b[32m'dentsply-sirona'\u001b[39m, product_count: \u001b[33m14532\u001b[39m },\n    { manufacturer: \u001b[32m'henry-schein'\u001b[39m, product_count: \u001b[33m11349\u001b[39m },\n    { manufacturer: \u001b[32m'coltene-whaledent'\u001b[39m, product_count: \u001b[33m9412\u001b[39m },\n    { manufacturer: \u001b[32m'gc-america'\u001b[39m, product_count: \u001b[33m7535\u001b[39m },\n    { manufacturer: \u001b[32m'3m-solventum'\u001b[39m, product_count: \u001b[33m7163\u001b[39m },\n    { manufacturer: \u001b[32m'premier-dental'\u001b[39m, product_count: \u001b[33m7108\u001b[39m },\n    { manufacturer: \u001b[32m'kulzer'\u001b[39m, product_count: \u001b[33m6394\u001b[39m },\n    { manufacturer: \u001b[32m'ss-white'\u001b[39m, product_count: \u001b[33m6060\u001b[39m },\n    { manufacturer: \u001b[32m'keystone-industries'\u001b[39m, product_count: \u001b[33m5532\u001b[39m },\n    { manufacturer: \u001b[32m'belmont'\u001b[39m, product_count: \u001b[33m5113\u001b[39m },\n    { manufacturer: \u001b[32m'meisinger'\u001b[39m, product_count: \u001b[33m4947\u001b[39m },\n    { manufacturer: \u001b[32m'ivoclar-vivadent'\u001b[39m, product_count: \u001b[33m4642\u001b[39m },\n    { manufacturer: \u001b[32m'shofu'\u001b[39m, product_count: \u001b[33m4580\u001b[39m },\n    { manufacturer: \u001b[32m'nordent'\u001b[39m, product_count: \u001b[33m4402\u001b[39m },\n    { manufacturer: \u001b[32m'pac-dent'\u001b[39m, product_count: \u001b[33m4164\u001b[39m },\n    { manufacturer: \u001b[32m'zirc'\u001b[39m, product_count: \u001b[33m3856\u001b[39m },\n    { manufacturer: \u001b[32m'integra-miltex'\u001b[39m, product_count: \u001b[33m3802\u001b[39m },\n    { manufacturer: \u001b[32m'benco'\u001b[39m, product_count: \u001b[33m3607\u001b[39m },\n    { manufacturer: \u001b[32m'crosstex'\u001b[39m, product_count: \u001b[33m3346\u001b[39m },\n    { manufacturer: \u001b[32m'darby'\u001b[39m, product_count: \u001b[33m3198\u001b[39m },\n    { manufacturer: \u001b[32m'american-eagle'\u001b[39m, product_count: \u001b[33m2893\u001b[39m },\n    { manufacturer: \u001b[32m'ortho-technology'\u001b[39m, product_count: \u001b[33m2848\u001b[39m },\n    { manufacturer: \u001b[32m'ortho-essentials'\u001b[39m, product_count: \u001b[33m2779\u001b[39m },\n    { manufacturer: \u001b[32m'quala'\u001b[39m, product_count: \u001b[33m2650\u001b[39m },\n    { manufacturer: \u001b[32m'kuraray'\u001b[39m, product_count: \u001b[33m2483\u001b[39m },\n    { manufacturer: \u001b[32m'a-titan'\u001b[39m, product_count: \u001b[33m2455\u001b[39m },\n    { manufacturer: \u001b[32m'ortho-organizers'\u001b[39m, product_count: \u001b[33m2431\u001b[39m },\n    { manufacturer: \u001b[32m'plasdent'\u001b[39m, product_count: \u001b[33m2249\u001b[39m },\n    { manufacturer: \u001b[32m'dentalez'\u001b[39m, product_count: \u001b[33m2236\u001b[39m },\n    { manufacturer: \u001b[32m'young-dental'\u001b[39m, product_count: \u001b[33m2180\u001b[39m },\n    { manufacturer: \u001b[32m'amann-girrbach'\u001b[39m, product_count: \u001b[33m2173\u001b[39m },\n    { manufacturer: \u001b[32m'advance'\u001b[39m, product_count: \u001b[33m2125\u001b[39m },\n    { manufacturer: \u001b[32m'sdi'\u001b[39m, product_count: \u001b[33m2125\u001b[39m },\n    { manufacturer: \u001b[32m'directa'\u001b[39m, product_count: \u001b[33m2070\u001b[39m },\n    { manufacturer: \u001b[32m'palmero'\u001b[39m, product_count: \u001b[33m1994\u001b[39m },\n    { manufacturer: \u001b[32m'medicom'\u001b[39m, product_count: \u001b[33m1953\u001b[39m },\n    { manufacturer: \u001b[32m'diadent'\u001b[39m, product_count: \u001b[33m1858\u001b[39m },\n    { manufacturer: \u001b[32m'voco'\u001b[39m, product_count: \u001b[33m1848\u001b[39m },\n    { manufacturer: \u001b[32m'vista-apex'\u001b[39m, product_count: \u001b[33m1786\u001b[39m },\n    { manufacturer: \u001b[32m'dedeco'\u001b[39m, product_count: \u001b[33m1715\u001b[39m },\n    { manufacturer: \u001b[32m'buffalo-dental'\u001b[39m, product_count: \u001b[33m1711\u001b[39m },\n    { manufacturer: \u001b[32m'safco-dental'\u001b[39m, product_count: \u001b[33m1700\u001b[39m },\n    { manufacturer: \u001b[32m'superior-uniform'\u001b[39m, product_count: \u001b[33m1700\u001b[39m },\n    { manufacturer: \u001b[32m'3b-orthodontics'\u001b[39m, product_count: \u001b[33m1669\u001b[39m },\n    { manufacturer: \u001b[32m'pulpdent'\u001b[39m, product_count: \u001b[33m1644\u001b[39m },\n    { manufacturer: \u001b[32m'microcopy'\u001b[39m, product_count: \u001b[33m1643\u001b[39m },\n    { manufacturer: \u001b[32m'wonderwink'\u001b[39m, product_count: \u001b[33m1583\u001b[39m },\n    { manufacturer: \u001b[32m'j-j-instruments'\u001b[39m, product_count: \u001b[33m1560\u001b[39m },\n    { manufacturer: \u001b[32m'dmg-america'\u001b[39m, product_count: \u001b[33m1543\u001b[39m },\n    { manufacturer: \u001b[32m'edge-endo'\u001b[39m, product_count: \u001b[33m1522\u001b[39m },\n    { manufacturer: \u001b[32m'midmark'\u001b[39m, product_count: \u001b[33m1511\u001b[39m },\n    { manufacturer: \u001b[32m'parkell'\u001b[39m, product_count: \u001b[33m1502\u001b[39m },\n    { manufacturer: \u001b[32m'aidusa'\u001b[39m, product_count: \u001b[33m1463\u001b[39m },\n    { manufacturer: \u001b[32m'denmat'\u001b[39m, product_count: \u001b[33m1456\u001b[39m },\n    { manufacturer: \u001b[32m'ansell'\u001b[39m, product_count: \u001b[33m1424\u001b[39m },\n    { manufacturer: \u001b[32m'valumax'\u001b[39m, product_count: \u001b[33m1368\u001b[39m },\n    { manufacturer: \u001b[32m'garrison-dental'\u001b[39m, product_count: \u001b[33m1349\u001b[39m },\n    { manufacturer: \u001b[32m'vita'\u001b[39m, product_count: \u001b[33m1302\u001b[39m },\n    { manufacturer: \u001b[32m'cargus'\u001b[39m, product_count: \u001b[33m1293\u001b[39m },\n    { manufacturer: \u001b[32m'lang-dental'\u001b[39m, product_count: \u001b[33m1218\u001b[39m },\n    { manufacturer: \u001b[32m'house-brand'\u001b[39m, product_count: \u001b[33m1159\u001b[39m },\n    { manufacturer: \u001b[32m'megagen'\u001b[39m, product_count: \u001b[33m1075\u001b[39m },\n    { manufacturer: \u001b[32m'dukal'\u001b[39m, product_count: \u001b[33m1024\u001b[39m },\n    { manufacturer: \u001b[32m'defend'\u001b[39m, product_count: \u001b[33m1007\u001b[39m },\n    { manufacturer: \u001b[32m'ace-surgical'\u001b[39m, product_count: \u001b[33m989\u001b[39m },\n    { manufacturer: \u001b[32m'danville-materials'\u001b[39m, product_count: \u001b[33m975\u001b[39m },\n    { manufacturer: \u001b[32m'sunstar'\u001b[39m, product_count: \u001b[33m968\u001b[39m },\n    { manufacturer: \u001b[32m'sultan-healthcare'\u001b[39m, product_count: \u001b[33m967\u001b[39m },\n    { manufacturer: \u001b[32m'plak-smacker'\u001b[39m, product_count: \u001b[33m965\u001b[39m },\n    { manufacturer: \u001b[32m'practicon'\u001b[39m, product_count: \u001b[33m960\u001b[39m },\n    { manufacturer: \u001b[32m'centrix'\u001b[39m, product_count: \u001b[33m934\u001b[39m },\n    { manufacturer: \u001b[32m'cardinal-health'\u001b[39m, product_count: \u001b[33m923\u001b[39m },\n    { manufacturer: \u001b[32m'flow-dental'\u001b[39m, product_count: \u001b[33m920\u001b[39m },\n    { manufacturer: \u001b[32m'nsk'\u001b[39m, product_count: \u001b[33m910\u001b[39m },\n    { manufacturer: \u001b[32m'js-dental'\u001b[39m, product_count: \u001b[33m877\u001b[39m },\n    { manufacturer: \u001b[32m'tokuyama'\u001b[39m, product_count: \u001b[33m860\u001b[39m },\n    { manufacturer: \u001b[32m'tidi-products'\u001b[39m, product_count: \u001b[33m854\u001b[39m },\n    { manufacturer: \u001b[32m'roydent'\u001b[39m, product_count: \u001b[33m853\u001b[39m },\n    { manufacturer: \u001b[32m'hager-worldwide'\u001b[39m, product_count: \u001b[33m849\u001b[39m },\n    { manufacturer: \u001b[32m'acteon'\u001b[39m, product_count: \u001b[33m838\u001b[39m },\n    { manufacturer: \u001b[32m'halyard'\u001b[39m, product_count: \u001b[33m831\u001b[39m },\n    { manufacturer: \u001b[32m'clinicians-choice'\u001b[39m, product_count: \u001b[33m827\u001b[39m },\n    { manufacturer: \u001b[32m'smistr'\u001b[39m, product_count: \u001b[33m821\u001b[39m },\n    { manufacturer: \u001b[32m'parker-hannifin'\u001b[39m, product_count: \u001b[33m798\u001b[39m },\n    { manufacturer: \u001b[32m'procter-gamble'\u001b[39m, product_count: \u001b[33m798\u001b[39m },\n    { manufacturer: \u001b[32m'ods'\u001b[39m, product_count: \u001b[33m786\u001b[39m },\n    { manufacturer: \u001b[32m'brasseler'\u001b[39m, product_count: \u001b[33m774\u001b[39m },\n    { manufacturer: \u001b[32m'accutron'\u001b[39m, product_count: \u001b[33m767\u001b[39m },\n    { manufacturer: \u001b[32m'waterpik'\u001b[39m, product_count: \u001b[33m760\u001b[39m },\n    { manufacturer: \u001b[32m'pdt'\u001b[39m, product_count: \u001b[33m734\u001b[39m },\n    { manufacturer: \u001b[32m'paradise-dental'\u001b[39m, product_count: \u001b[33m727\u001b[39m },\n    { manufacturer: \u001b[32m'air-techniques'\u001b[39m, product_count: \u001b[33m706\u001b[39m },\n    { manufacturer: \u001b[32m'dci-international'\u001b[39m, product_count: \u001b[33m701\u001b[39m },\n    { manufacturer: \u001b[32m'spring-health'\u001b[39m, product_count: \u001b[33m700\u001b[39m },\n    { manufacturer: \u001b[32m'sagemax'\u001b[39m, product_count: \u001b[33m684\u001b[39m },\n    { manufacturer: \u001b[32m'essential-dental'\u001b[39m, product_count: \u001b[33m672\u001b[39m },\n    { manufacturer: \u001b[32m'ismile-dental'\u001b[39m, product_count: \u001b[33m671\u001b[39m },\n    { manufacturer: \u001b[32m'acero-crowns'\u001b[39m, product_count: \u001b[33m658\u001b[39m }\n  ]\n}\n"}, {"text": "Sellers API response: {\n  success: \u001b[33mtrue\u001b[39m,\n  message: \u001b[32m'Sellers retrieved successfully'\u001b[39m,\n  sellers: [\n    { seller: \u001b[32m'benco'\u001b[39m, product_count: \u001b[33m82084\u001b[39m },\n    { seller: \u001b[32m'henry'\u001b[39m, product_count: \u001b[33m76867\u001b[39m },\n    { seller: \u001b[32m'midwest'\u001b[39m, product_count: \u001b[33m41946\u001b[39m },\n    { seller: \u001b[32m'darby'\u001b[39m, product_count: \u001b[33m27778\u001b[39m },\n    { seller: \u001b[32m'net32'\u001b[39m, product_count: \u001b[33m26862\u001b[39m },\n    { seller: \u001b[32m'optimus'\u001b[39m, product_count: \u001b[33m20714\u001b[39m },\n    { seller: \u001b[32m'safco'\u001b[39m, product_count: \u001b[33m17669\u001b[39m },\n    { seller: \u001b[32m'dds'\u001b[39m, product_count: \u001b[33m17315\u001b[39m },\n    { seller: \u001b[32m'tdsc'\u001b[39m, product_count: \u001b[33m16583\u001b[39m },\n    { seller: \u001b[32m'frontier'\u001b[39m, product_count: \u001b[33m11260\u001b[39m }\n  ]\n}\n"}, {"text": "Filter functionality test completed successfully\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-31T18:51:50.034Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "b40160f6fba3e7a72973-95a5f6793e4d7154dc66", "file": "search-functionality.spec.ts", "line": 104, "column": 3}, {"title": "should test complete search workflow", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 18, "parallelIndex": 1, "status": "failed", "duration": 8997, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n\n    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:149:31", "location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 149}, "snippet": "\u001b[0m \u001b[90m 147 |\u001b[39m     \u001b[90m// Find and interact with search input\u001b[39m\n \u001b[90m 148 |\u001b[39m     \u001b[36mconst\u001b[39m searchInput \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder*=\"Search\"]'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 149 |\u001b[39m     \u001b[36mawait\u001b[39m expect(searchInput)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 150 |\u001b[39m     \n \u001b[90m 151 |\u001b[39m     \u001b[90m// Type search query\u001b[39m\n \u001b[90m 152 |\u001b[39m     \u001b[36mawait\u001b[39m searchInput\u001b[33m.\u001b[39mfill(\u001b[32m'dental implant'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 149}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n\n\n\u001b[0m \u001b[90m 147 |\u001b[39m     \u001b[90m// Find and interact with search input\u001b[39m\n \u001b[90m 148 |\u001b[39m     \u001b[36mconst\u001b[39m searchInput \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder*=\"Search\"]'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 149 |\u001b[39m     \u001b[36mawait\u001b[39m expect(searchInput)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 150 |\u001b[39m     \n \u001b[90m 151 |\u001b[39m     \u001b[90m// Type search query\u001b[39m\n \u001b[90m 152 |\u001b[39m     \u001b[36mawait\u001b[39m searchInput\u001b[33m.\u001b[39mfill(\u001b[32m'dental implant'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:149:31\u001b[22m"}], "stdout": [{"text": "Testing complete search workflow...\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-31T18:51:51.250Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-515be-st-complete-search-workflow-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-515be-st-complete-search-workflow-Mobile-Chrome\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-515be-st-complete-search-workflow-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 149}}], "status": "unexpected"}], "id": "b40160f6fba3e7a72973-79c3567f51efe1b20e6d", "file": "search-functionality.spec.ts", "line": 137, "column": 3}, {"title": "should display search suggestions when typing", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 21, "parallelIndex": 3, "status": "failed", "duration": 8168, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n\n    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:17:31", "location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 17}, "snippet": "\u001b[0m \u001b[90m 15 |\u001b[39m     \u001b[90m// Find the search input\u001b[39m\n \u001b[90m 16 |\u001b[39m     \u001b[36mconst\u001b[39m searchInput \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder*=\"Search\"]'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 17 |\u001b[39m     \u001b[36mawait\u001b[39m expect(searchInput)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 18 |\u001b[39m     \n \u001b[90m 19 |\u001b[39m     \u001b[90m// Type in the search input\u001b[39m\n \u001b[90m 20 |\u001b[39m     \u001b[36mawait\u001b[39m searchInput\u001b[33m.\u001b[39mfill(\u001b[32m'dental'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 17}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n\n\n\u001b[0m \u001b[90m 15 |\u001b[39m     \u001b[90m// Find the search input\u001b[39m\n \u001b[90m 16 |\u001b[39m     \u001b[36mconst\u001b[39m searchInput \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder*=\"Search\"]'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 17 |\u001b[39m     \u001b[36mawait\u001b[39m expect(searchInput)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 18 |\u001b[39m     \n \u001b[90m 19 |\u001b[39m     \u001b[90m// Type in the search input\u001b[39m\n \u001b[90m 20 |\u001b[39m     \u001b[36mawait\u001b[39m searchInput\u001b[33m.\u001b[39mfill(\u001b[32m'dental'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:17:31\u001b[22m"}], "stdout": [{"text": "Testing search suggestions...\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-31T18:51:54.621Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-78a37-rch-suggestions-when-typing-Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-78a37-rch-suggestions-when-typing-Mobile-Safari\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-78a37-rch-suggestions-when-typing-Mobile-Safari\\error-context.md"}], "errorLocation": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 17}}], "status": "unexpected"}], "id": "b40160f6fba3e7a72973-288809c4737e82e77ef6", "file": "search-functionality.spec.ts", "line": 12, "column": 3}, {"title": "should perform search and show results", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 19, "parallelIndex": 2, "status": "failed", "duration": 7467, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n\n    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:45:31", "location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 45}, "snippet": "\u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[90m// Find the search input\u001b[39m\n \u001b[90m 44 |\u001b[39m     \u001b[36mconst\u001b[39m searchInput \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder*=\"Search\"]'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 45 |\u001b[39m     \u001b[36mawait\u001b[39m expect(searchInput)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 46 |\u001b[39m     \n \u001b[90m 47 |\u001b[39m     \u001b[90m// Type and press Enter\u001b[39m\n \u001b[90m 48 |\u001b[39m     \u001b[36mawait\u001b[39m searchInput\u001b[33m.\u001b[39mfill(\u001b[32m'dental'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 45}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n\n\n\u001b[0m \u001b[90m 43 |\u001b[39m     \u001b[90m// Find the search input\u001b[39m\n \u001b[90m 44 |\u001b[39m     \u001b[36mconst\u001b[39m searchInput \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder*=\"Search\"]'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 45 |\u001b[39m     \u001b[36mawait\u001b[39m expect(searchInput)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 46 |\u001b[39m     \n \u001b[90m 47 |\u001b[39m     \u001b[90m// Type and press Enter\u001b[39m\n \u001b[90m 48 |\u001b[39m     \u001b[36mawait\u001b[39m searchInput\u001b[33m.\u001b[39mfill(\u001b[32m'dental'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:45:31\u001b[22m"}], "stdout": [{"text": "Testing search results...\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-31T18:51:54.346Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-68681-orm-search-and-show-results-Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-68681-orm-search-and-show-results-Mobile-Safari\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-68681-orm-search-and-show-results-Mobile-Safari\\error-context.md"}], "errorLocation": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 45}}], "status": "unexpected"}], "id": "b40160f6fba3e7a72973-c942677f32ed276d464d", "file": "search-functionality.spec.ts", "line": 40, "column": 3}, {"title": "should test API endpoints directly", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 20, "parallelIndex": 0, "status": "passed", "duration": 4135, "errors": [], "stdout": [{"text": "Testing API endpoints...\n"}, {"text": "Suggestions API response: {\n  success: \u001b[33mtrue\u001b[39m,\n  message: \u001b[32m'Suggestions retrieved successfully'\u001b[39m,\n  query: \u001b[32m'dental'\u001b[39m,\n  suggestions: [\n    {\n      id: \u001b[33m26974\u001b[39m,\n      mfr: \u001b[32m'77023-1'\u001b[39m,\n      name: \u001b[32m'Seal-Tight® Auto-Lock Adapter Type \"D\" Dental-Ez'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/3143-117/seal-tight-auto-lock-adapter-type-d-dental-ez'\u001b[39m,\n      maincat: \u001b[32m'Disposables'\u001b[39m,\n      brand: \u001b[32m'Kerr TotalCare'\u001b[39m,\n      manufactured_by: \u001b[32m'kavo-kerr'\u001b[39m,\n      category: \u001b[32m'Disposables & Infection Control'\u001b[39m\n    },\n    {\n      id: \u001b[33m119535\u001b[39m,\n      mfr: \u001b[32m'31009-2'\u001b[39m,\n      name: \u001b[32m'Chairmount Dental-Eze NuSimplicity'\u001b[39m,\n      seller: \u001b[32m'dds'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://ddsdentalsupplies.com/chairmount-dental-eze-nusimplicity/'\u001b[39m,\n      maincat: \u001b[32m'Miscellaneous'\u001b[39m,\n      brand: \u001b[32m'Crosstex International'\u001b[39m,\n      manufactured_by: \u001b[32m'crosstex'\u001b[39m,\n      category: \u001b[32m'Miscellaneous'\u001b[39m\n    },\n    {\n      id: \u001b[33m119536\u001b[39m,\n      mfr: \u001b[32m'31009-1'\u001b[39m,\n      name: \u001b[32m'Chairmount Dental-Eze Simplicity'\u001b[39m,\n      seller: \u001b[32m'dds'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://ddsdentalsupplies.com/chairmount-dental-eze-simplicity/'\u001b[39m,\n      maincat: \u001b[32m'Miscellaneous'\u001b[39m,\n      brand: \u001b[32m'Crosstex International'\u001b[39m,\n      manufactured_by: \u001b[32m'crosstex'\u001b[39m,\n      category: \u001b[32m'Miscellaneous'\u001b[39m\n    },\n    {\n      id: \u001b[33m262197\u001b[39m,\n      mfr: \u001b[32m'FGSS557'\u001b[39m,\n      name: \u001b[32m'DentalRee Dental Ree FG #557 SS (Short Shank) Straight Fissure Crosscut Carbide Bur, 10/Pk'\u001b[39m,\n      seller: \u001b[32m'net32'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://www.net32.com/ec/dentalree-dental-ree-fg-557-ss-short-d-181969?queryID=bb4766df2db464254b5a30d4dc456052'\u001b[39m,\n      maincat: \u001b[32m'Burs & diamonds'\u001b[39m,\n      brand: \u001b[32m'DentalRee'\u001b[39m,\n      manufactured_by: \u001b[32m'dentalree'\u001b[39m,\n      category: \u001b[32m'Rotary Instruments (Burs, Diamonds, Polishers)'\u001b[39m\n    },\n    {\n      id: \u001b[33m263274\u001b[39m,\n      mfr: \u001b[32m'FGSS57'\u001b[39m,\n      name: \u001b[32m'DentalRee Dental Ree FG #57 SS (Short Shank) Straight Fissure Carbide Bur, 10/Pk'\u001b[39m,\n      seller: \u001b[32m'net32'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://www.net32.com/ec/dentalree-dental-ree-fg-57-ss-short-d-181970?queryID=bd26415c286636703f53fa6eec443455'\u001b[39m,\n      maincat: \u001b[32m'Burs & diamonds'\u001b[39m,\n      brand: \u001b[32m'DentalRee'\u001b[39m,\n      manufactured_by: \u001b[32m'dentalree'\u001b[39m,\n      category: \u001b[32m'Rotary Instruments (Burs, Diamonds, Polishers)'\u001b[39m\n    },\n    {\n      id: \u001b[33m283631\u001b[39m,\n      mfr: \u001b[32m'BB030'\u001b[39m,\n      name: \u001b[32m'Block-4-Bite Disposable X-Ray Bite Blocks, Fits Phillips, Dental-Ez Panolite'\u001b[39m,\n      seller: \u001b[32m'net32'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://www.net32.com/ec/block4bite-disposable-xray-bite-blocks-fits-phillips-d-54856?queryID=56125ad20d6ec0db166024722df5de5c'\u001b[39m,\n      maincat: \u001b[32m'X-Ray products'\u001b[39m,\n      brand: \u001b[32m'Plasdent'\u001b[39m,\n      manufactured_by: \u001b[32m'plasdent'\u001b[39m,\n      category: \u001b[32m'Diagnostic Tools & Imaging'\u001b[39m\n    },\n    {\n      id: \u001b[33m332360\u001b[39m,\n      mfr: \u001b[32m'261547'\u001b[39m,\n      name: \u001b[32m'Dental-EZ Handpiece Swivels, 4-Line'\u001b[39m,\n      seller: \u001b[32m'tdsc'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://www.tdsc.com/All-Categories/Dental-Supplies/Handpieces/Handpiece-Tools-%26-Maintenance/Handpiece-Swivels/Dental-EZ-Handpiece-Swivels/p/220396-1?q=&productListName=Category%20Page&dimension8=&pageRef=CATEGORY'\u001b[39m,\n      maincat: \u001b[32m'Handpieces'\u001b[39m,\n      brand: \u001b[32m'STARDENTAL-DENTAL EZ'\u001b[39m,\n      manufactured_by: \u001b[32m'dentalez'\u001b[39m,\n      category: \u001b[32m'Rotary Instruments (Burs, Diamonds, Polishers)'\u001b[39m\n    },\n    {\n      id: \u001b[33m22003\u001b[39m,\n      mfr: \u001b[32m'5133-026'\u001b[39m,\n      name: \u001b[32m'Benco Dental™ Dental Disperse Tip 19GA Yellow Pack of 20'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/5133-026/benco-dental-dental-disperse-tip-19ga-yellow-pack-'\u001b[39m,\n      maincat: \u001b[32m'Cosmetic Dentistry'\u001b[39m,\n      brand: \u001b[32m'BENCO'\u001b[39m,\n      manufactured_by: \u001b[32m'benco'\u001b[39m,\n      category: \u001b[32m'Restorative Materials (Composites, Amalgams, Glass Ionomers)'\u001b[39m\n    },\n    {\n      id: \u001b[33m22004\u001b[39m,\n      mfr: \u001b[32m'5133-035'\u001b[39m,\n      name: \u001b[32m'Benco Dental™ Dental Disperse Tip 19GA Yellow Pack of 100'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/5133-035/benco-dental-dental-disperse-tip-19ga-yellow-pack-'\u001b[39m,\n      maincat: \u001b[32m'Cosmetic Dentistry'\u001b[39m,\n      brand: \u001b[32m'BENCO'\u001b[39m,\n      manufactured_by: \u001b[32m'benco'\u001b[39m,\n      category: \u001b[32m'Restorative Materials (Composites, Amalgams, Glass Ionomers)'\u001b[39m\n    },\n    {\n      id: \u001b[33m25910\u001b[39m,\n      mfr: \u001b[32m'3229-927'\u001b[39m,\n      name: \u001b[32m'Benco Dental™ Blue Thin Rubber Latex Dental Dam 5\"x5\" Box of 52'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/3229-927/benco-dental-blue-thin-rubber-latex-dental-dam-5x5'\u001b[39m,\n      maincat: \u001b[32m'Dental Dam'\u001b[39m,\n      brand: \u001b[32m'BENCO'\u001b[39m,\n      manufactured_by: \u001b[32m'benco'\u001b[39m,\n      category: \u001b[32m'Disposables & Infection Control'\u001b[39m\n    }\n  ]\n}\n"}, {"text": "Search API response: {\n  success: \u001b[33mtrue\u001b[39m,\n  message: \u001b[32m'Search completed successfully (cached)'\u001b[39m,\n  query: \u001b[32m'dental'\u001b[39m,\n  search_type: \u001b[32m'fulltext'\u001b[39m,\n  total: \u001b[33m5810\u001b[39m,\n  results: [\n    {\n      id: \u001b[33m26974\u001b[39m,\n      mfr: \u001b[32m'77023-1'\u001b[39m,\n      name: \u001b[32m'Seal-Tight® Auto-Lock Adapter Type \"D\" Dental-Ez'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/3143-117/seal-tight-auto-lock-adapter-type-d-dental-ez'\u001b[39m,\n      maincat: \u001b[32m'Disposables'\u001b[39m,\n      brand: \u001b[32m'Kerr TotalCare'\u001b[39m,\n      manufactured_by: \u001b[32m'kavo-kerr'\u001b[39m,\n      category: \u001b[32m'Disposables & Infection Control'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'77023-1 | Seal-Tight® Auto-Lock Adapter Type \"D\" Dental-Ez'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m119535\u001b[39m,\n      mfr: \u001b[32m'31009-2'\u001b[39m,\n      name: \u001b[32m'Chairmount Dental-Eze NuSimplicity'\u001b[39m,\n      url: \u001b[32m'https://ddsdentalsupplies.com/chairmount-dental-eze-nusimplicity/'\u001b[39m,\n      maincat: \u001b[32m'Miscellaneous'\u001b[39m,\n      brand: \u001b[32m'Crosstex International'\u001b[39m,\n      manufactured_by: \u001b[32m'crosstex'\u001b[39m,\n      category: \u001b[32m'Miscellaneous'\u001b[39m,\n      seller: \u001b[32m'dds'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'31009-2 | Chairmount Dental-Eze NuSimplicity'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m119536\u001b[39m,\n      mfr: \u001b[32m'31009-1'\u001b[39m,\n      name: \u001b[32m'Chairmount Dental-Eze Simplicity'\u001b[39m,\n      url: \u001b[32m'https://ddsdentalsupplies.com/chairmount-dental-eze-simplicity/'\u001b[39m,\n      maincat: \u001b[32m'Miscellaneous'\u001b[39m,\n      brand: \u001b[32m'Crosstex International'\u001b[39m,\n      manufactured_by: \u001b[32m'crosstex'\u001b[39m,\n      category: \u001b[32m'Miscellaneous'\u001b[39m,\n      seller: \u001b[32m'dds'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'31009-1 | Chairmount Dental-Eze Simplicity'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m262197\u001b[39m,\n      mfr: \u001b[32m'FGSS557'\u001b[39m,\n      name: \u001b[32m'DentalRee Dental Ree FG #557 SS (Short Shank) Straight Fissure Crosscut Carbide Bur, 10/Pk'\u001b[39m,\n      url: \u001b[32m'https://www.net32.com/ec/dentalree-dental-ree-fg-557-ss-short-d-181969?queryID=bb4766df2db464254b5a30d4dc456052'\u001b[39m,\n      maincat: \u001b[32m'Burs & diamonds'\u001b[39m,\n      brand: \u001b[32m'DentalRee'\u001b[39m,\n      manufactured_by: \u001b[32m'dentalree'\u001b[39m,\n      category: \u001b[32m'Rotary Instruments (Burs, Diamonds, Polishers)'\u001b[39m,\n      seller: \u001b[32m'net32'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'FGSS557 | DentalRee Dental Ree FG #557 SS (Short Shank) Straight Fissure Crosscut Carbide Bur, 10/Pk'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m263274\u001b[39m,\n      mfr: \u001b[32m'FGSS57'\u001b[39m,\n      name: \u001b[32m'DentalRee Dental Ree FG #57 SS (Short Shank) Straight Fissure Carbide Bur, 10/Pk'\u001b[39m,\n      url: \u001b[32m'https://www.net32.com/ec/dentalree-dental-ree-fg-57-ss-short-d-181970?queryID=bd26415c286636703f53fa6eec443455'\u001b[39m,\n      maincat: \u001b[32m'Burs & diamonds'\u001b[39m,\n      brand: \u001b[32m'DentalRee'\u001b[39m,\n      manufactured_by: \u001b[32m'dentalree'\u001b[39m,\n      category: \u001b[32m'Rotary Instruments (Burs, Diamonds, Polishers)'\u001b[39m,\n      seller: \u001b[32m'net32'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'FGSS57 | DentalRee Dental Ree FG #57 SS (Short Shank) Straight Fissure Carbide Bur, 10/Pk'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m283631\u001b[39m,\n      mfr: \u001b[32m'BB030'\u001b[39m,\n      name: \u001b[32m'Block-4-Bite Disposable X-Ray Bite Blocks, Fits Phillips, Dental-Ez Panolite'\u001b[39m,\n      url: \u001b[32m'https://www.net32.com/ec/block4bite-disposable-xray-bite-blocks-fits-phillips-d-54856?queryID=56125ad20d6ec0db166024722df5de5c'\u001b[39m,\n      maincat: \u001b[32m'X-Ray products'\u001b[39m,\n      brand: \u001b[32m'Plasdent'\u001b[39m,\n      manufactured_by: \u001b[32m'plasdent'\u001b[39m,\n      category: \u001b[32m'Diagnostic Tools & Imaging'\u001b[39m,\n      seller: \u001b[32m'net32'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'BB030 | Block-4-Bite Disposable X-Ray Bite Blocks, Fits Phillips, Dental-Ez Panolite'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m332360\u001b[39m,\n      mfr: \u001b[32m'261547'\u001b[39m,\n      name: \u001b[32m'Dental-EZ Handpiece Swivels, 4-Line'\u001b[39m,\n      url: \u001b[32m'https://www.tdsc.com/All-Categories/Dental-Supplies/Handpieces/Handpiece-Tools-%26-Maintenance/Handpiece-Swivels/Dental-EZ-Handpiece-Swivels/p/220396-1?q=&productListName=Category%20Page&dimension8=&pageRef=CATEGORY'\u001b[39m,\n      maincat: \u001b[32m'Handpieces'\u001b[39m,\n      brand: \u001b[32m'STARDENTAL-DENTAL EZ'\u001b[39m,\n      manufactured_by: \u001b[32m'dentalez'\u001b[39m,\n      category: \u001b[32m'Rotary Instruments (Burs, Diamonds, Polishers)'\u001b[39m,\n      seller: \u001b[32m'tdsc'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'261547 | Dental-EZ Handpiece Swivels, 4-Line'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m22003\u001b[39m,\n      mfr: \u001b[32m'5133-026'\u001b[39m,\n      name: \u001b[32m'Benco Dental™ Dental Disperse Tip 19GA Yellow Pack of 20'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/5133-026/benco-dental-dental-disperse-tip-19ga-yellow-pack-'\u001b[39m,\n      maincat: \u001b[32m'Cosmetic Dentistry'\u001b[39m,\n      brand: \u001b[32m'BENCO'\u001b[39m,\n      manufactured_by: \u001b[32m'benco'\u001b[39m,\n      category: \u001b[32m'Restorative Materials (Composites, Amalgams, Glass Ionomers)'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'5133-026 | Benco Dental™ Dental Disperse Tip 19GA Yellow Pack of 20'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m22004\u001b[39m,\n      mfr: \u001b[32m'5133-035'\u001b[39m,\n      name: \u001b[32m'Benco Dental™ Dental Disperse Tip 19GA Yellow Pack of 100'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/5133-035/benco-dental-dental-disperse-tip-19ga-yellow-pack-'\u001b[39m,\n      maincat: \u001b[32m'Cosmetic Dentistry'\u001b[39m,\n      brand: \u001b[32m'BENCO'\u001b[39m,\n      manufactured_by: \u001b[32m'benco'\u001b[39m,\n      category: \u001b[32m'Restorative Materials (Composites, Amalgams, Glass Ionomers)'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'5133-035 | Benco Dental™ Dental Disperse Tip 19GA Yellow Pack of 100'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    },\n    {\n      id: \u001b[33m25910\u001b[39m,\n      mfr: \u001b[32m'3229-927'\u001b[39m,\n      name: \u001b[32m'Benco Dental™ Blue Thin Rubber Latex Dental Dam 5\"x5\" Box of 52'\u001b[39m,\n      url: \u001b[32m'https://shop.benco.com/Product/3229-927/benco-dental-blue-thin-rubber-latex-dental-dam-5x5'\u001b[39m,\n      maincat: \u001b[32m'Dental Dam'\u001b[39m,\n      brand: \u001b[32m'BENCO'\u001b[39m,\n      manufactured_by: \u001b[32m'benco'\u001b[39m,\n      category: \u001b[32m'Disposables & Infection Control'\u001b[39m,\n      seller: \u001b[32m'benco'\u001b[39m,\n      price: \u001b[32m'Price not available'\u001b[39m,\n      search_text: \u001b[32m'3229-927 | Benco Dental™ Blue Thin Rubber Latex Dental Dam 5\"x5\" Box of 52'\u001b[39m,\n      rank: \u001b[1mnull\u001b[22m,\n      similarity_score: \u001b[1mnull\u001b[22m\n    }\n  ],\n  pagination: {\n    total: \u001b[33m5810\u001b[39m,\n    page: \u001b[33m1\u001b[39m,\n    per_page: \u001b[33m10\u001b[39m,\n    total_pages: \u001b[33m581\u001b[39m,\n    has_next: \u001b[33mtrue\u001b[39m,\n    has_prev: \u001b[33mfalse\u001b[39m\n  },\n  filters_applied: \u001b[1mnull\u001b[22m,\n  search_time_ms: \u001b[33m1.5411376953125\u001b[39m\n}\n"}, {"text": "Popular searches API response: {\n  success: \u001b[33mtrue\u001b[39m,\n  message: \u001b[32m'Popular searches retrieved successfully'\u001b[39m,\n  popular_searches: [\n    { term: \u001b[32m'Instruments'\u001b[39m, count: \u001b[33m36356\u001b[39m },\n    { term: \u001b[32m'Endodontics'\u001b[39m, count: \u001b[33m21769\u001b[39m },\n    { term: \u001b[32m'Burs & Diamonds'\u001b[39m, count: \u001b[33m19053\u001b[39m },\n    { term: \u001b[32m'Infection Control'\u001b[39m, count: \u001b[33m15785\u001b[39m },\n    { term: \u001b[32m'Hu-<PERSON><PERSON><PERSON>'\u001b[39m, count: \u001b[33m14752\u001b[39m }\n  ]\n}\n"}, {"text": "API endpoints test completed successfully\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-31T18:51:54.730Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "b40160f6fba3e7a72973-32c107686d432568c0f8", "file": "search-functionality.spec.ts", "line": 70, "column": 3}, {"title": "should test filter functionality", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 22, "parallelIndex": 4, "status": "passed", "duration": 3273, "errors": [], "stdout": [{"text": "Testing filter functionality...\n"}, {"text": "Categories API response: {\n  success: \u001b[33mtrue\u001b[39m,\n  message: \u001b[32m'Categories retrieved successfully'\u001b[39m,\n  categories: [\n    {\n      category: \u001b[32m'Rotary Instruments (Burs, Diamonds, Polishers)'\u001b[39m,\n      product_count: \u001b[33m49726\u001b[39m\n    },\n    { category: \u001b[32m'Instruments (Hand)'\u001b[39m, product_count: \u001b[33m42762\u001b[39m },\n    {\n      category: \u001b[32m'Disposables & Infection Control'\u001b[39m,\n      product_count: \u001b[33m36221\u001b[39m\n    },\n    {\n      category: \u001b[32m'Restorative Materials (Composites, Amalgams, Glass Ionomers)'\u001b[39m,\n      product_count: \u001b[33m34559\u001b[39m\n    },\n    { category: \u001b[32m'Endodontics'\u001b[39m, product_count: \u001b[33m26829\u001b[39m },\n    { category: \u001b[32m'Laboratory Products'\u001b[39m, product_count: \u001b[33m24842\u001b[39m },\n    { category: \u001b[32m'Equipment (General)'\u001b[39m, product_count: \u001b[33m17145\u001b[39m },\n    { category: \u001b[32m'Orthodontics'\u001b[39m, product_count: \u001b[33m14315\u001b[39m },\n    { category: \u001b[32m'Preventative & Oral Hygiene'\u001b[39m, product_count: \u001b[33m14299\u001b[39m },\n    {\n      category: \u001b[32m'Prosthodontics (Crowns, Bridges, Dentures)'\u001b[39m,\n      product_count: \u001b[33m13344\u001b[39m\n    },\n    { category: \u001b[32m'Miscellaneous'\u001b[39m, product_count: \u001b[33m13308\u001b[39m },\n    { category: \u001b[32m'Surgical Supplies'\u001b[39m, product_count: \u001b[33m13292\u001b[39m },\n    { category: \u001b[32m'Impression Materials'\u001b[39m, product_count: \u001b[33m11897\u001b[39m },\n    { category: \u001b[32m'Finishing & Polishing'\u001b[39m, product_count: \u001b[33m9048\u001b[39m },\n    { category: \u001b[32m'Diagnostic Tools & Imaging'\u001b[39m, product_count: \u001b[33m6099\u001b[39m },\n    { category: \u001b[32m'Anesthetics & Pain Management'\u001b[39m, product_count: \u001b[33m6041\u001b[39m },\n    { category: \u001b[32m'Cements & Liners'\u001b[39m, product_count: \u001b[33m3207\u001b[39m },\n    { category: \u001b[32m'Bonding & Adhesives'\u001b[39m, product_count: \u001b[33m1217\u001b[39m },\n    { category: \u001b[32m'Waxes & Modeling Materials'\u001b[39m, product_count: \u001b[33m761\u001b[39m },\n    { category: \u001b[32m'Curing Lights'\u001b[39m, product_count: \u001b[33m166\u001b[39m }\n  ]\n}\n"}, {"text": "Manufacturers API response: {\n  success: \u001b[33mtrue\u001b[39m,\n  message: \u001b[32m'Manufacturers retrieved successfully'\u001b[39m,\n  manufacturers: [\n    { manufacturer: \u001b[32m'kavo-kerr'\u001b[39m, product_count: \u001b[33m25380\u001b[39m },\n    { manufacturer: \u001b[32m'hu-friedy'\u001b[39m, product_count: \u001b[33m17549\u001b[39m },\n    { manufacturer: \u001b[32m'dentsply-sirona'\u001b[39m, product_count: \u001b[33m14532\u001b[39m },\n    { manufacturer: \u001b[32m'henry-schein'\u001b[39m, product_count: \u001b[33m11349\u001b[39m },\n    { manufacturer: \u001b[32m'coltene-whaledent'\u001b[39m, product_count: \u001b[33m9412\u001b[39m },\n    { manufacturer: \u001b[32m'gc-america'\u001b[39m, product_count: \u001b[33m7535\u001b[39m },\n    { manufacturer: \u001b[32m'3m-solventum'\u001b[39m, product_count: \u001b[33m7163\u001b[39m },\n    { manufacturer: \u001b[32m'premier-dental'\u001b[39m, product_count: \u001b[33m7108\u001b[39m },\n    { manufacturer: \u001b[32m'kulzer'\u001b[39m, product_count: \u001b[33m6394\u001b[39m },\n    { manufacturer: \u001b[32m'ss-white'\u001b[39m, product_count: \u001b[33m6060\u001b[39m },\n    { manufacturer: \u001b[32m'keystone-industries'\u001b[39m, product_count: \u001b[33m5532\u001b[39m },\n    { manufacturer: \u001b[32m'belmont'\u001b[39m, product_count: \u001b[33m5113\u001b[39m },\n    { manufacturer: \u001b[32m'meisinger'\u001b[39m, product_count: \u001b[33m4947\u001b[39m },\n    { manufacturer: \u001b[32m'ivoclar-vivadent'\u001b[39m, product_count: \u001b[33m4642\u001b[39m },\n    { manufacturer: \u001b[32m'shofu'\u001b[39m, product_count: \u001b[33m4580\u001b[39m },\n    { manufacturer: \u001b[32m'nordent'\u001b[39m, product_count: \u001b[33m4402\u001b[39m },\n    { manufacturer: \u001b[32m'pac-dent'\u001b[39m, product_count: \u001b[33m4164\u001b[39m },\n    { manufacturer: \u001b[32m'zirc'\u001b[39m, product_count: \u001b[33m3856\u001b[39m },\n    { manufacturer: \u001b[32m'integra-miltex'\u001b[39m, product_count: \u001b[33m3802\u001b[39m },\n    { manufacturer: \u001b[32m'benco'\u001b[39m, product_count: \u001b[33m3607\u001b[39m },\n    { manufacturer: \u001b[32m'crosstex'\u001b[39m, product_count: \u001b[33m3346\u001b[39m },\n    { manufacturer: \u001b[32m'darby'\u001b[39m, product_count: \u001b[33m3198\u001b[39m },\n    { manufacturer: \u001b[32m'american-eagle'\u001b[39m, product_count: \u001b[33m2893\u001b[39m },\n    { manufacturer: \u001b[32m'ortho-technology'\u001b[39m, product_count: \u001b[33m2848\u001b[39m },\n    { manufacturer: \u001b[32m'ortho-essentials'\u001b[39m, product_count: \u001b[33m2779\u001b[39m },\n    { manufacturer: \u001b[32m'quala'\u001b[39m, product_count: \u001b[33m2650\u001b[39m },\n    { manufacturer: \u001b[32m'kuraray'\u001b[39m, product_count: \u001b[33m2483\u001b[39m },\n    { manufacturer: \u001b[32m'a-titan'\u001b[39m, product_count: \u001b[33m2455\u001b[39m },\n    { manufacturer: \u001b[32m'ortho-organizers'\u001b[39m, product_count: \u001b[33m2431\u001b[39m },\n    { manufacturer: \u001b[32m'plasdent'\u001b[39m, product_count: \u001b[33m2249\u001b[39m },\n    { manufacturer: \u001b[32m'dentalez'\u001b[39m, product_count: \u001b[33m2236\u001b[39m },\n    { manufacturer: \u001b[32m'young-dental'\u001b[39m, product_count: \u001b[33m2180\u001b[39m },\n    { manufacturer: \u001b[32m'amann-girrbach'\u001b[39m, product_count: \u001b[33m2173\u001b[39m },\n    { manufacturer: \u001b[32m'advance'\u001b[39m, product_count: \u001b[33m2125\u001b[39m },\n    { manufacturer: \u001b[32m'sdi'\u001b[39m, product_count: \u001b[33m2125\u001b[39m },\n    { manufacturer: \u001b[32m'directa'\u001b[39m, product_count: \u001b[33m2070\u001b[39m },\n    { manufacturer: \u001b[32m'palmero'\u001b[39m, product_count: \u001b[33m1994\u001b[39m },\n    { manufacturer: \u001b[32m'medicom'\u001b[39m, product_count: \u001b[33m1953\u001b[39m },\n    { manufacturer: \u001b[32m'diadent'\u001b[39m, product_count: \u001b[33m1858\u001b[39m },\n    { manufacturer: \u001b[32m'voco'\u001b[39m, product_count: \u001b[33m1848\u001b[39m },\n    { manufacturer: \u001b[32m'vista-apex'\u001b[39m, product_count: \u001b[33m1786\u001b[39m },\n    { manufacturer: \u001b[32m'dedeco'\u001b[39m, product_count: \u001b[33m1715\u001b[39m },\n    { manufacturer: \u001b[32m'buffalo-dental'\u001b[39m, product_count: \u001b[33m1711\u001b[39m },\n    { manufacturer: \u001b[32m'safco-dental'\u001b[39m, product_count: \u001b[33m1700\u001b[39m },\n    { manufacturer: \u001b[32m'superior-uniform'\u001b[39m, product_count: \u001b[33m1700\u001b[39m },\n    { manufacturer: \u001b[32m'3b-orthodontics'\u001b[39m, product_count: \u001b[33m1669\u001b[39m },\n    { manufacturer: \u001b[32m'pulpdent'\u001b[39m, product_count: \u001b[33m1644\u001b[39m },\n    { manufacturer: \u001b[32m'microcopy'\u001b[39m, product_count: \u001b[33m1643\u001b[39m },\n    { manufacturer: \u001b[32m'wonderwink'\u001b[39m, product_count: \u001b[33m1583\u001b[39m },\n    { manufacturer: \u001b[32m'j-j-instruments'\u001b[39m, product_count: \u001b[33m1560\u001b[39m },\n    { manufacturer: \u001b[32m'dmg-america'\u001b[39m, product_count: \u001b[33m1543\u001b[39m },\n    { manufacturer: \u001b[32m'edge-endo'\u001b[39m, product_count: \u001b[33m1522\u001b[39m },\n    { manufacturer: \u001b[32m'midmark'\u001b[39m, product_count: \u001b[33m1511\u001b[39m },\n    { manufacturer: \u001b[32m'parkell'\u001b[39m, product_count: \u001b[33m1502\u001b[39m },\n    { manufacturer: \u001b[32m'aidusa'\u001b[39m, product_count: \u001b[33m1463\u001b[39m },\n    { manufacturer: \u001b[32m'denmat'\u001b[39m, product_count: \u001b[33m1456\u001b[39m },\n    { manufacturer: \u001b[32m'ansell'\u001b[39m, product_count: \u001b[33m1424\u001b[39m },\n    { manufacturer: \u001b[32m'valumax'\u001b[39m, product_count: \u001b[33m1368\u001b[39m },\n    { manufacturer: \u001b[32m'garrison-dental'\u001b[39m, product_count: \u001b[33m1349\u001b[39m },\n    { manufacturer: \u001b[32m'vita'\u001b[39m, product_count: \u001b[33m1302\u001b[39m },\n    { manufacturer: \u001b[32m'cargus'\u001b[39m, product_count: \u001b[33m1293\u001b[39m },\n    { manufacturer: \u001b[32m'lang-dental'\u001b[39m, product_count: \u001b[33m1218\u001b[39m },\n    { manufacturer: \u001b[32m'house-brand'\u001b[39m, product_count: \u001b[33m1159\u001b[39m },\n    { manufacturer: \u001b[32m'megagen'\u001b[39m, product_count: \u001b[33m1075\u001b[39m },\n    { manufacturer: \u001b[32m'dukal'\u001b[39m, product_count: \u001b[33m1024\u001b[39m },\n    { manufacturer: \u001b[32m'defend'\u001b[39m, product_count: \u001b[33m1007\u001b[39m },\n    { manufacturer: \u001b[32m'ace-surgical'\u001b[39m, product_count: \u001b[33m989\u001b[39m },\n    { manufacturer: \u001b[32m'danville-materials'\u001b[39m, product_count: \u001b[33m975\u001b[39m },\n    { manufacturer: \u001b[32m'sunstar'\u001b[39m, product_count: \u001b[33m968\u001b[39m },\n    { manufacturer: \u001b[32m'sultan-healthcare'\u001b[39m, product_count: \u001b[33m967\u001b[39m },\n    { manufacturer: \u001b[32m'plak-smacker'\u001b[39m, product_count: \u001b[33m965\u001b[39m },\n    { manufacturer: \u001b[32m'practicon'\u001b[39m, product_count: \u001b[33m960\u001b[39m },\n    { manufacturer: \u001b[32m'centrix'\u001b[39m, product_count: \u001b[33m934\u001b[39m },\n    { manufacturer: \u001b[32m'cardinal-health'\u001b[39m, product_count: \u001b[33m923\u001b[39m },\n    { manufacturer: \u001b[32m'flow-dental'\u001b[39m, product_count: \u001b[33m920\u001b[39m },\n    { manufacturer: \u001b[32m'nsk'\u001b[39m, product_count: \u001b[33m910\u001b[39m },\n    { manufacturer: \u001b[32m'js-dental'\u001b[39m, product_count: \u001b[33m877\u001b[39m },\n    { manufacturer: \u001b[32m'tokuyama'\u001b[39m, product_count: \u001b[33m860\u001b[39m },\n    { manufacturer: \u001b[32m'tidi-products'\u001b[39m, product_count: \u001b[33m854\u001b[39m },\n    { manufacturer: \u001b[32m'roydent'\u001b[39m, product_count: \u001b[33m853\u001b[39m },\n    { manufacturer: \u001b[32m'hager-worldwide'\u001b[39m, product_count: \u001b[33m849\u001b[39m },\n    { manufacturer: \u001b[32m'acteon'\u001b[39m, product_count: \u001b[33m838\u001b[39m },\n    { manufacturer: \u001b[32m'halyard'\u001b[39m, product_count: \u001b[33m831\u001b[39m },\n    { manufacturer: \u001b[32m'clinicians-choice'\u001b[39m, product_count: \u001b[33m827\u001b[39m },\n    { manufacturer: \u001b[32m'smistr'\u001b[39m, product_count: \u001b[33m821\u001b[39m },\n    { manufacturer: \u001b[32m'parker-hannifin'\u001b[39m, product_count: \u001b[33m798\u001b[39m },\n    { manufacturer: \u001b[32m'procter-gamble'\u001b[39m, product_count: \u001b[33m798\u001b[39m },\n    { manufacturer: \u001b[32m'ods'\u001b[39m, product_count: \u001b[33m786\u001b[39m },\n    { manufacturer: \u001b[32m'brasseler'\u001b[39m, product_count: \u001b[33m774\u001b[39m },\n    { manufacturer: \u001b[32m'accutron'\u001b[39m, product_count: \u001b[33m767\u001b[39m },\n    { manufacturer: \u001b[32m'waterpik'\u001b[39m, product_count: \u001b[33m760\u001b[39m },\n    { manufacturer: \u001b[32m'pdt'\u001b[39m, product_count: \u001b[33m734\u001b[39m },\n    { manufacturer: \u001b[32m'paradise-dental'\u001b[39m, product_count: \u001b[33m727\u001b[39m },\n    { manufacturer: \u001b[32m'air-techniques'\u001b[39m, product_count: \u001b[33m706\u001b[39m },\n    { manufacturer: \u001b[32m'dci-international'\u001b[39m, product_count: \u001b[33m701\u001b[39m },\n    { manufacturer: \u001b[32m'spring-health'\u001b[39m, product_count: \u001b[33m700\u001b[39m },\n    { manufacturer: \u001b[32m'sagemax'\u001b[39m, product_count: \u001b[33m684\u001b[39m },\n    { manufacturer: \u001b[32m'essential-dental'\u001b[39m, product_count: \u001b[33m672\u001b[39m },\n    { manufacturer: \u001b[32m'ismile-dental'\u001b[39m, product_count: \u001b[33m671\u001b[39m },\n    { manufacturer: \u001b[32m'acero-crowns'\u001b[39m, product_count: \u001b[33m658\u001b[39m }\n  ]\n}\n"}, {"text": "Sellers API response: {\n  success: \u001b[33mtrue\u001b[39m,\n  message: \u001b[32m'Sellers retrieved successfully'\u001b[39m,\n  sellers: [\n    { seller: \u001b[32m'benco'\u001b[39m, product_count: \u001b[33m82084\u001b[39m },\n    { seller: \u001b[32m'henry'\u001b[39m, product_count: \u001b[33m76867\u001b[39m },\n    { seller: \u001b[32m'midwest'\u001b[39m, product_count: \u001b[33m41946\u001b[39m },\n    { seller: \u001b[32m'darby'\u001b[39m, product_count: \u001b[33m27778\u001b[39m },\n    { seller: \u001b[32m'net32'\u001b[39m, product_count: \u001b[33m26862\u001b[39m },\n    { seller: \u001b[32m'optimus'\u001b[39m, product_count: \u001b[33m20714\u001b[39m },\n    { seller: \u001b[32m'safco'\u001b[39m, product_count: \u001b[33m17669\u001b[39m },\n    { seller: \u001b[32m'dds'\u001b[39m, product_count: \u001b[33m17315\u001b[39m },\n    { seller: \u001b[32m'tdsc'\u001b[39m, product_count: \u001b[33m16583\u001b[39m },\n    { seller: \u001b[32m'frontier'\u001b[39m, product_count: \u001b[33m11260\u001b[39m }\n  ]\n}\n"}, {"text": "Filter functionality test completed successfully\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-31T18:51:56.927Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "b40160f6fba3e7a72973-156812984f181a23d7f3", "file": "search-functionality.spec.ts", "line": 104, "column": 3}, {"title": "should test complete search workflow", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 23, "parallelIndex": 5, "status": "failed", "duration": 9425, "error": {"message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n\n    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:149:31", "location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 149}, "snippet": "\u001b[0m \u001b[90m 147 |\u001b[39m     \u001b[90m// Find and interact with search input\u001b[39m\n \u001b[90m 148 |\u001b[39m     \u001b[36mconst\u001b[39m searchInput \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder*=\"Search\"]'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 149 |\u001b[39m     \u001b[36mawait\u001b[39m expect(searchInput)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 150 |\u001b[39m     \n \u001b[90m 151 |\u001b[39m     \u001b[90m// Type search query\u001b[39m\n \u001b[90m 152 |\u001b[39m     \u001b[36mawait\u001b[39m searchInput\u001b[33m.\u001b[39mfill(\u001b[32m'dental implant'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 149}, "message": "Error: \u001b[31mTimed out 5000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('input[placeholder*=\"Search\"]').first()\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 5000ms\u001b[22m\n\u001b[2m  - waiting for locator('input[placeholder*=\"Search\"]').first()\u001b[22m\n\n\n\u001b[0m \u001b[90m 147 |\u001b[39m     \u001b[90m// Find and interact with search input\u001b[39m\n \u001b[90m 148 |\u001b[39m     \u001b[36mconst\u001b[39m searchInput \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'input[placeholder*=\"Search\"]'\u001b[39m)\u001b[33m.\u001b[39mfirst()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 149 |\u001b[39m     \u001b[36mawait\u001b[39m expect(searchInput)\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                               \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 150 |\u001b[39m     \n \u001b[90m 151 |\u001b[39m     \u001b[90m// Type search query\u001b[39m\n \u001b[90m 152 |\u001b[39m     \u001b[36mawait\u001b[39m searchInput\u001b[33m.\u001b[39mfill(\u001b[32m'dental implant'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts:149:31\u001b[22m"}], "stdout": [{"text": "Testing complete search workflow...\n"}], "stderr": [], "retry": 0, "startTime": "2025-07-31T18:51:57.503Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-515be-st-complete-search-workflow-Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-515be-st-complete-search-workflow-Mobile-Safari\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "D:\\online work\\profident\\frontend\\test-results\\search-functionality-Searc-515be-st-complete-search-workflow-Mobile-Safari\\error-context.md"}], "errorLocation": {"file": "D:\\online work\\profident\\frontend\\tests\\search-functionality.spec.ts", "column": 31, "line": 149}}], "status": "unexpected"}], "id": "b40160f6fba3e7a72973-2729c562597dc6c58692", "file": "search-functionality.spec.ts", "line": 137, "column": 3}]}]}], "errors": [], "stats": {"startTime": "2025-07-31T18:50:50.264Z", "duration": 76876.94099999999, "expected": 8, "skipped": 0, "unexpected": 17, "flaky": 0}}