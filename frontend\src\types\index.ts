// User types
export interface User {
  id: string;
  email: string;
  full_name: string;
  is_active: boolean;
  created_at: string;
}

export interface AuthTokens {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
}

export interface LoginRequest {
  email: string;
  password: string;
}

export interface RegisterRequest {
  email: string;
  password: string;
  full_name: string;
}

export interface AuthResponse {
  user: User;
  token: AuthTokens;
}

// Product types
export interface Product {
  id: number;
  name: string;
  mfr?: string;
  brand?: string;
  seller: string;
  maincat?: string;
  category?: string;
  manufactured_by?: string;
  price?: string;
  url: string;
  search_text: string;
  created_at: string;
  updated_at: string;
  rank?: number;
  similarity_score?: number;
}

export interface ProductSummary {
  id: number;
  name: string;
  brand?: string;
  price?: string;
  seller: string;
}

// Search types
export interface SearchRequest {
  q: string;
  limit?: number;
  offset?: number;
  search_type?: "fulltext" | "similarity" | "hybrid";
  category?: string;
  brand?: string;
  seller?: string;
  manufactured_by?: string;
}

export interface SearchResponse {
  success: boolean;
  message: string;
  query: string;
  search_type: string;
  total: number;
  results: Product[];
  pagination: {
    limit: number;
    offset: number;
    has_next: boolean;
    has_prev: boolean;
  };
  search_time_ms: number;
}

export interface SearchSuggestion {
  term: string;
  count?: number;
}

export interface ProductSuggestion {
  id: number;
  mfr: string;
  name: string;
  seller: string;
  price?: string;
  url: string;
  maincat?: string;
  brand?: string;
  manufactured_by?: string;
  category?: string;
}

export interface SuggestionsResponse {
  success: boolean;
  message: string;
  query: string;
  suggestions: ProductSuggestion[];
}

export interface PopularSearchesResponse {
  success: boolean;
  message: string;
  popular_searches: SearchSuggestion[];
}

export interface CategoryResponse {
  success: boolean;
  message: string;
  categories: Array<{
    category: string;
    product_count: number;
  }>;
}

export interface ManufacturerResponse {
  success: boolean;
  message: string;
  manufacturers: Array<{
    manufacturer: string;
    product_count: number;
  }>;
}

export interface SellerResponse {
  success: boolean;
  message: string;
  sellers: Array<{
    seller: string;
    product_count: number;
  }>;
}

// Shopping List types
export interface ShoppingListItem {
  id: number;
  shopping_list_id: number;
  product_id: number;
  quantity: number;
  notes?: string;
  added_at: string;
  product?: ProductSummary;
}

export interface ShoppingListSummary {
  total_items: number;
  remaining_items: number;
  purchased_items: number;
  total_quantity: number;
  completion_percentage: number;
}

export interface ShoppingList {
  id: number;
  user_id: string;
  name: string;
  description?: string;
  is_default?: boolean;
  created_at: string;
  updated_at: string;
  items?: ShoppingListItem[];
  summary?: ShoppingListSummary;
}

export interface ShoppingListCreate {
  name: string;
  description?: string;
  is_default?: boolean;
}

export interface ShoppingListUpdate {
  name?: string;
  description?: string;
  is_default?: boolean;
}

export interface ShoppingListItemCreate {
  product_id: number;
  quantity: number;
  notes?: string;
}

export interface ShoppingListItemUpdate {
  quantity?: number;
  notes?: string;
}

export interface ShoppingListAnalysis {
  total_estimated_cost: number;
  items_with_prices: number;
  items_without_prices: number;
  seller_breakdown: {
    seller: string;
    items: number;
    total_cost: number;
  }[];
}

// API Response types
export interface APIResponse<T = any> {
  success: boolean;
  message: string;
  data?: T;
}

export interface PaginatedResponse<T> {
  success: boolean;
  message: string;
  data: {
    items: T[];
    pagination: {
      total: number;
      page: number;
      per_page: number;
      total_pages: number;
      has_next: boolean;
      has_prev: boolean;
    };
  };
}

// Filter types
export interface SearchFilters {
  category?: string;
  brand?: string;
  seller?: string;
  manufactured_by?: string;
  search_type?: "fulltext" | "similarity" | "hybrid";
}

// UI State types
export interface LoadingState {
  isLoading: boolean;
  error?: string;
}

export interface PaginationState {
  page: number;
  per_page: number;
  total: number;
  total_pages: number;
}
