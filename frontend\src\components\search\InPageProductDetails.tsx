import React from 'react';
import { X, ArrowLeft } from 'lucide-react';
import ProductDetailsCard from './ProductDetailsCard';
import SameFromOtherSellers from './SameFromOtherSellers';
import { useProductByName, useProductMatching } from '../../hooks/useProductMatching';
import { SearchFilters } from '../../types';
import ErrorBoundary from '../ErrorBoundary';

interface InPageProductDetailsProps {
  productName: string;
  activeFilters?: SearchFilters;
  onClose: () => void;
  className?: string;
}

const InPageProductDetails: React.FC<InPageProductDetailsProps> = ({
  productName,
  activeFilters,
  onClose,
  className = ""
}) => {
  // Get the main product
  const { 
    data: mainProduct, 
    isLoading: productLoading, 
    error: productError 
  } = useProductByName(productName);

  // Get matching products with same MFR
  const { 
    data: matchingData, 
    isLoading: matchingLoading, 
    error: matchingError 
  } = useProductMatching({
    mfr: mainProduct?.mfr,
    enabled: !!mainProduct?.mfr,
    filters: activeFilters,
    limit: 12
  });

  // Filter out the main product from matching results
  const otherSellerProducts = matchingData?.products?.filter(
    product => product.id !== mainProduct?.id
  ) || [];

  if (productLoading) {
    return (
      <div className={`bg-white border border-gray-200 rounded-lg shadow-sm p-8 ${className}`}>
        <div className="animate-pulse">
          <div className="flex items-center justify-between mb-6">
            <div className="h-6 bg-gray-200 rounded w-1/3"></div>
            <div className="h-8 w-8 bg-gray-200 rounded"></div>
          </div>
          <div className="space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            <div className="h-20 bg-gray-200 rounded"></div>
          </div>
        </div>
      </div>
    );
  }

  if (productError || !mainProduct) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-6 ${className}`}>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-red-800">
            Product Not Found
          </h3>
          <button
            onClick={onClose}
            className="text-red-600 hover:text-red-800"
          >
            <X className="h-5 w-5" />
          </button>
        </div>
        <p className="text-red-600 mb-4">
          Unable to find product: "{productName}"
        </p>
        <button
          onClick={onClose}
          className="inline-flex items-center text-sm text-red-600 hover:text-red-800"
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to Search
        </button>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with Close Button */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl font-semibold text-gray-900">
          Product Details
        </h2>
        <button
          onClick={onClose}
          className="p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
          aria-label="Close product details"
        >
          <X className="h-5 w-5" />
        </button>
      </div>

      {/* Main Product Card */}
      <ErrorBoundary>
        <ProductDetailsCard 
          product={mainProduct} 
          className="shadow-md"
        />
      </ErrorBoundary>

      {/* Same from Other Sellers */}
      {mainProduct.mfr && (
        <ErrorBoundary>
          <SameFromOtherSellers
            mfr={mainProduct.mfr}
            products={otherSellerProducts}
            isLoading={matchingLoading}
            error={matchingError}
            activeFilters={activeFilters}
            className="shadow-sm"
          />
        </ErrorBoundary>
      )}

      {/* Back to Search Button */}
      <div className="flex justify-center pt-4">
        <button
          onClick={onClose}
          className="inline-flex items-center px-6 py-3 border border-gray-300 text-base font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors"
        >
          <ArrowLeft className="h-5 w-5 mr-2" />
          Back to Search
        </button>
      </div>
    </div>
  );
};

export default InPageProductDetails;
