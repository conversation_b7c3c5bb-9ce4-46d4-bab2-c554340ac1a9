import { useEffect, useState } from "react";
import { useSearchParams } from "react-router-dom";
import { ChevronLeft, ChevronRight, Grid, List } from "lucide-react";
import { useSearchStore, buildSearchRequest } from "../stores/searchStore";
import { useAutoSearch } from "../hooks/useSearch";
import SearchResults from "../components/search/SearchResults";
import SearchFilters from "../components/search/SearchFilters";
import { cn } from "../lib/utils";

const SearchPage = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [viewMode, setViewMode] = useState<"list" | "grid">("list");

  const {
    query,
    results,
    total,
    isLoading,
    error,
    pagination,
    setPagination,
    setQuery,
  } = useSearchStore();

  const { performSearch } = useAutoSearch();

  // Initialize search from URL params
  useEffect(() => {
    const urlQuery = searchParams.get("q");
    const urlPage = parseInt(searchParams.get("page") || "1");

    if (urlQuery && urlQuery !== query) {
      setQuery(urlQuery);
    }

    if (urlPage !== pagination.page) {
      setPagination({ page: urlPage });
    }
  }, [searchParams, query, pagination.page, setQuery, setPagination]);

  // Perform search when query or pagination changes
  useEffect(() => {
    if (query) {
      const searchRequest = buildSearchRequest(useSearchStore.getState());
      performSearch(searchRequest);

      // Update URL
      const newParams = new URLSearchParams(searchParams);
      newParams.set("q", query);
      newParams.set("page", pagination.page.toString());
      setSearchParams(newParams);
    }
  }, [query, pagination.page]); // Remove performSearch from dependencies to prevent infinite loop

  const handlePageChange = (newPage: number) => {
    setPagination({ page: newPage });
    window.scrollTo({ top: 0, behavior: "smooth" });
  };

  const totalPages = Math.ceil(total / pagination.limit);
  const hasNextPage = pagination.page < totalPages;
  const hasPrevPage = pagination.page > 1;

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pages = [];
    const maxVisible = 7;
    const current = pagination.page;

    if (totalPages <= maxVisible) {
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      if (current <= 4) {
        for (let i = 1; i <= 5; i++) pages.push(i);
        pages.push("...");
        pages.push(totalPages);
      } else if (current >= totalPages - 3) {
        pages.push(1);
        pages.push("...");
        for (let i = totalPages - 4; i <= totalPages; i++) pages.push(i);
      } else {
        pages.push(1);
        pages.push("...");
        for (let i = current - 1; i <= current + 1; i++) pages.push(i);
        pages.push("...");
        pages.push(totalPages);
      }
    }

    return pages;
  };

  if (!query) {
    return (
      <div className="max-w-7xl mx-auto">
        <div className="text-center py-12">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">
            Search Products
          </h1>
          <p className="text-gray-600 mb-8">
            Enter a search term to find dental products from our database of
            339K+ items.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto">
      <div className="flex flex-col lg:flex-row gap-8">
        {/* Filters Sidebar */}
        <div className="lg:w-80 flex-shrink-0">
          <div className="sticky top-24">
            <SearchFilters />
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 min-w-0">
          {/* Search Header */}
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              Search Results
            </h1>
            {query && (
              <p className="text-gray-600">
                Results for "<span className="font-medium">{query}</span>"
              </p>
            )}
          </div>

          {/* Results Header */}
          {total > 0 && !isLoading && (
            <div className="flex items-center justify-between mb-6 pb-4 border-b border-gray-200">
              <div className="text-sm text-gray-600">
                Showing {(pagination.page - 1) * pagination.limit + 1}-
                {Math.min(pagination.page * pagination.limit, total)} of{" "}
                {total.toLocaleString()} results
              </div>

              {/* View Mode Toggle */}
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">View:</span>
                <div className="flex border border-gray-300 rounded-md">
                  <button
                    onClick={() => setViewMode("list")}
                    className={cn(
                      "p-2 text-sm font-medium transition-colors",
                      viewMode === "list"
                        ? "bg-primary-600 text-white"
                        : "text-gray-700 hover:bg-gray-50"
                    )}
                  >
                    <List className="h-4 w-4" />
                  </button>
                  <button
                    onClick={() => setViewMode("grid")}
                    className={cn(
                      "p-2 text-sm font-medium transition-colors border-l border-gray-300",
                      viewMode === "grid"
                        ? "bg-primary-600 text-white"
                        : "text-gray-700 hover:bg-gray-50"
                    )}
                  >
                    <Grid className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Error State */}
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
              <div className="text-red-800">
                <strong>Error:</strong> {error}
              </div>
            </div>
          )}

          {/* Search Results */}
          <SearchResults
            results={results}
            isLoading={isLoading}
            query={query}
          />

          {/* Pagination */}
          {total > pagination.limit && !isLoading && (
            <div className="mt-8 flex items-center justify-between">
              <div className="text-sm text-gray-700">
                Page {pagination.page} of {totalPages}
              </div>

              <div className="flex items-center space-x-2">
                {/* Previous Button */}
                <button
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={!hasPrevPage}
                  className={cn(
                    "inline-flex items-center px-3 py-2 border text-sm font-medium rounded-md transition-colors",
                    hasPrevPage
                      ? "border-gray-300 text-gray-700 bg-white hover:bg-gray-50"
                      : "border-gray-200 text-gray-400 bg-gray-50 cursor-not-allowed"
                  )}
                >
                  <ChevronLeft className="h-4 w-4 mr-1" />
                  Previous
                </button>

                {/* Page Numbers */}
                <div className="hidden sm:flex space-x-1">
                  {getPageNumbers().map((page, index) => (
                    <button
                      key={index}
                      onClick={() =>
                        typeof page === "number" && handlePageChange(page)
                      }
                      disabled={page === "..."}
                      className={cn(
                        "px-3 py-2 text-sm font-medium rounded-md transition-colors",
                        page === pagination.page
                          ? "bg-primary-600 text-white"
                          : page === "..."
                          ? "text-gray-400 cursor-default"
                          : "text-gray-700 hover:bg-gray-100"
                      )}
                    >
                      {page}
                    </button>
                  ))}
                </div>

                {/* Next Button */}
                <button
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={!hasNextPage}
                  className={cn(
                    "inline-flex items-center px-3 py-2 border text-sm font-medium rounded-md transition-colors",
                    hasNextPage
                      ? "border-gray-300 text-gray-700 bg-white hover:bg-gray-50"
                      : "border-gray-200 text-gray-400 bg-gray-50 cursor-not-allowed"
                  )}
                >
                  Next
                  <ChevronRight className="h-4 w-4 ml-1" />
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SearchPage;
