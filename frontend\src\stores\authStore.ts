import { create } from "zustand";
import { persist } from "zustand/middleware";
import { User, AuthTokens, LoginRequest, RegisterRequest } from "../types";
import apiClient from "../lib/api";

interface AuthState {
  user: User | null;
  tokens: AuthTokens | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

interface AuthActions {
  login: (credentials: LoginRequest) => Promise<void>;
  register: (userData: RegisterRequest) => Promise<void>;
  logout: () => void;
  clearError: () => void;
  initializeAuth: () => Promise<void>;
  refreshAuth: () => Promise<void>;
}

type AuthStore = AuthState & AuthActions;

export const useAuthStore = create<AuthStore>()(
  persist(
    (set, get) => ({
      // Initial state - Unauthenticated
      user: null,
      tokens: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // Actions
      login: async (credentials: LoginRequest) => {
        set({ isLoading: true, error: null });

        try {
          const response = await apiClient.login(credentials);
          const { user, token } = response;

          // Store tokens in localStorage
          localStorage.setItem("access_token", token.access_token);
          localStorage.setItem("refresh_token", token.refresh_token);

          set({
            user,
            tokens: token,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error: any) {
          const errorMessage = error.response?.data?.detail || "Login failed";
          set({
            isLoading: false,
            error: errorMessage,
            user: null,
            tokens: null,
            isAuthenticated: false,
          });
          throw error;
        }
      },

      register: async (userData: RegisterRequest) => {
        set({ isLoading: true, error: null });

        try {
          const response = await apiClient.register(userData);
          const { user, token } = response;

          // Store tokens in localStorage
          localStorage.setItem("access_token", token.access_token);
          localStorage.setItem("refresh_token", token.refresh_token);

          set({
            user,
            tokens: token,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error: any) {
          const errorMessage =
            error.response?.data?.detail || "Registration failed";
          set({
            isLoading: false,
            error: errorMessage,
            user: null,
            tokens: null,
            isAuthenticated: false,
          });
          throw error;
        }
      },

      logout: () => {
        // Clear localStorage
        localStorage.removeItem("access_token");
        localStorage.removeItem("refresh_token");

        set({
          user: null,
          tokens: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        });
      },

      clearError: () => {
        set({ error: null });
      },

      initializeAuth: async () => {
        const accessToken = localStorage.getItem("access_token");
        const refreshToken = localStorage.getItem("refresh_token");

        if (!accessToken || !refreshToken) {
          return;
        }

        set({ isLoading: true });

        try {
          // Try to get current user to validate token
          const user = await apiClient.getCurrentUser();

          set({
            user,
            tokens: {
              access_token: accessToken,
              refresh_token: refreshToken,
              token_type: "bearer",
              expires_in: 3600, // Default value
            },
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });
        } catch (error) {
          // Token is invalid, clear auth state
          get().logout();
          set({ isLoading: false });
        }
      },

      refreshAuth: async () => {
        const refreshToken = localStorage.getItem("refresh_token");

        if (!refreshToken) {
          get().logout();
          return;
        }

        try {
          const tokens = await apiClient.refreshToken(refreshToken);

          // Store new tokens
          localStorage.setItem("access_token", tokens.access_token);
          localStorage.setItem("refresh_token", tokens.refresh_token);

          set({
            tokens,
            error: null,
          });
        } catch (error) {
          // Refresh failed, logout user
          get().logout();
          throw error;
        }
      },
    }),
    {
      name: "auth-storage",
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
